<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Export Businesses Using Overpass (No Google API)</title>
  <style> 
    /* CSS Custom Properties for consistent theming */
    :root {
      --primary-color: #004B8D;
      --secondary-color: #10a37f;
      --danger-color: #d32f2f;
      --success-color: #1976d2;
      --border-color: #ccc;
      --light-border: #e5e5e5;
      --background-light: #f9f9f9;
      --background-lighter: #f5f5f5;
      --text-color: #333;
      --text-muted: #666;
      --text-light: #555;
      --shadow-light: 0 1px 3px rgba(0,0,0,0.1);
      --shadow-medium: 0 4px 12px rgba(0,0,0,0.3);
      --border-radius: 4px;
      --border-radius-large: 8px;
      --spacing-xs: 0.25rem;
      --spacing-sm: 0.5rem;
      --spacing-md: 1rem;
      --spacing-lg: 1.5rem;
      --spacing-xl: 2rem;
    }

    /* Reset and base styles */
    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
      margin: 0;
      padding: var(--spacing-sm);
      color: var(--text-color);
      line-height: 1.5;
      background-color: #fff;
    }

    /* Mobile-first responsive container */
    .container {
      max-width: 100%;
      margin: 0 auto;
      padding: 0 var(--spacing-sm);
    }

    /* Branded Header Styles */
    .branded-header {
      background: linear-gradient(135deg, var(--primary-color) 0%, #003a6b 100%);
      color: white;
      padding: var(--spacing-md) var(--spacing-sm);
      margin-bottom: var(--spacing-lg);
      box-shadow: var(--shadow-medium);
    }

    .header-container {
      max-width: 1200px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: 1fr;
      gap: var(--spacing-md);
      align-items: center;
    }

    .header-brand {
      text-align: center;
    }

    .brand-logo {
      font-size: 2rem;
      font-weight: 700;
      margin: 0;
      color: white;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .brand-tagline {
      font-size: 0.9rem;
      margin: var(--spacing-xs) 0 0 0;
      opacity: 0.9;
      font-weight: 300;
    }

    .header-nav {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
      align-items: center;
    }

    .header-nav a {
      color: white;
      text-decoration: none;
      font-weight: 500;
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--border-radius);
      transition: all 0.2s ease;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .header-nav a:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .header-contact {
      text-align: center;
      font-size: 0.85rem;
      opacity: 0.9;
    }

    .header-contact a {
      color: white;
      text-decoration: none;
      margin: 0 var(--spacing-xs);
      transition: opacity 0.2s ease;
    }

    .header-contact a:hover {
      opacity: 1;
      text-decoration: underline;
    }

    /* Header responsive styles */
    @media screen and (min-width: 600px) {
      .header-container {
        grid-template-columns: 1fr auto 1fr;
        align-items: center;
      }

      .header-brand {
        text-align: left;
      }

      .header-nav {
        flex-direction: row;
        gap: var(--spacing-md);
      }

      .header-contact {
        text-align: right;
      }
    }

    @media screen and (min-width: 768px) {
      .branded-header {
        padding: var(--spacing-lg) var(--spacing-md);
      }

      .brand-logo {
        font-size: 2.5rem;
      }

      .brand-tagline {
        font-size: 1rem;
      }

      .header-nav a {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 1rem;
      }
    }

    @media screen and (min-width: 1024px) {
      .header-container {
        grid-template-columns: 2fr auto 2fr;
      }

      .brand-logo {
        font-size: 3rem;
      }

      .header-nav {
        gap: var(--spacing-lg);
      }
    }

    /* Categories container - Mobile first */
    .categories-container {
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      padding: var(--spacing-sm);
      margin-bottom: var(--spacing-md);
      width: 100%;
      overflow: hidden;
      box-sizing: border-box;
    }

    .categories-header {
      font-weight: bold;
      margin-bottom: var(--spacing-sm);
      padding-bottom: var(--spacing-sm);
      border-bottom: 1px solid #eee;
    }

    .key-groups-container {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-sm);
      width: 100%;
      align-items: flex-start;
      justify-content: flex-start;
    }

    .key-group {
      border: 1px solid var(--light-border);
      border-radius: var(--border-radius);
      padding: var(--spacing-sm);
      background-color: var(--background-light);
      box-shadow: var(--shadow-light);
      box-sizing: border-box;
      /* Dynamic width based on content */
      width: fit-content;
      min-width: 200px; /* Minimum width for usability */
      max-width: 100%; /* Prevent overflow on small screens */
      /* Add smooth transitions for responsive changes */
      transition: all 0.3s ease;
      /* Ensure proper spacing for delete button */
      position: relative;
    }

    .key-header {
      font-weight: bold;
      margin-bottom: var(--spacing-sm);
      padding-bottom: var(--spacing-xs);
      border-bottom: 1px solid #eee;
      color: var(--text-light);
      position: relative;
    }

    .key-header .group-delete-btn {
      display: none;
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      color: var(--danger-color);
      font-weight: bold;
      cursor: pointer;
      font-size: 1.1rem;
      line-height: 1;
      padding: 2px 4px;
      border-radius: var(--border-radius);
      background: rgba(255, 255, 255, 0.9);
      transition: all 0.2s ease;
      /* Ensure the delete button stays within the container */
      z-index: 10;
    }

    .key-header .group-delete-btn:hover {
      background: var(--danger-color);
      color: white;
    }

    .key-header:hover .group-delete-btn {
      display: block;
    }

    .key-items {
      display: grid;
      grid-template-columns: 1fr;
      gap: var(--spacing-xs);
      max-height: 300px;
      overflow-y: auto;
    }

    .key-items.columns-1 {
      grid-template-columns: 1fr;
    }

    .key-items.columns-2 {
      grid-template-columns: repeat(2, 1fr);
    }

    .key-items.columns-3 {
      grid-template-columns: repeat(3, 1fr);
    }

    .key-items.scrollable {
      max-height: 300px;
      overflow-y: auto;
    }

    /* Actions and buttons */
    .actions {
      margin: var(--spacing-md) 0;
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    button {
      padding: var(--spacing-sm) var(--spacing-md);
      font-size: 1rem;
      cursor: pointer;
      border: 1px solid var(--primary-color);
      background: var(--primary-color);
      color: white;
      border-radius: var(--border-radius);
      transition: all 0.2s ease;
      font-family: inherit;
    }

    button:hover:not(:disabled) {
      background: #003a6b;
      border-color: #003a6b;
    }

    button:disabled {
      background: var(--text-muted);
      border-color: var(--text-muted);
      cursor: not-allowed;
      opacity: 0.6;
    }

    /* Log area */
    #log {
      margin-top: var(--spacing-md);
      white-space: pre-wrap;
      background: var(--background-lighter);
      padding: var(--spacing-md);
      border-radius: var(--border-radius);
      height: 200px;
      overflow-y: auto;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 0.85rem;
      border: 1px solid var(--light-border);
    }

    /* Results Container Styles */
    .results-container {
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius-large);
      padding: var(--spacing-md);
      margin-top: var(--spacing-lg);
      background: linear-gradient(135deg, #f8fffe 0%, var(--background-light) 100%);
      box-shadow: var(--shadow-light);
      display: none; /* Hidden by default */
    }

    .results-container.has-results {
      display: block;
    }

    .results-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-md);
      padding-bottom: var(--spacing-sm);
      border-bottom: 1px solid var(--light-border);
    }

    .results-header h3 {
      color: var(--primary-color);
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }

    .results-count {
      background: var(--primary-color);
      color: white;
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--border-radius);
      font-size: 0.85rem;
      font-weight: 500;
    }

    .results-actions {
      display: flex;
      gap: var(--spacing-sm);
      align-items: center;
    }

    .download-all-files-btn {
      background: var(--primary-color);
      border: 1px solid var(--primary-color);
      color: white;
      padding: var(--spacing-xs) var(--spacing-md);
      border-radius: var(--border-radius);
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-right: var(--spacing-md);
    }

    .download-all-files-btn:hover:not(:disabled) {
      background: #003a6b;
      border-color: #003a6b;
      transform: translateY(-1px);
      box-shadow: var(--shadow-light);
    }

    .download-all-files-btn:disabled {
      background: var(--text-muted);
      border-color: var(--text-muted);
      cursor: not-allowed;
      opacity: 0.6;
      transform: none;
      box-shadow: none;
    }

    .clear-results-btn {
      background: transparent;
      border: 1px solid var(--danger-color);
      color: var(--danger-color);
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--border-radius);
      font-size: 0.85rem;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .clear-results-btn:hover {
      background: var(--danger-color);
      color: white;
    }

    .downloads-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: var(--spacing-sm);
    }

    .download-link {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-md);
      background: white;
      border: 1px solid var(--light-border);
      border-radius: var(--border-radius);
      text-decoration: none;
      color: var(--text-color);
      transition: all 0.2s ease;
      box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }

    .download-link:hover {
      text-decoration: none;
      border-color: var(--primary-color);
      box-shadow: var(--shadow-light);
      transform: translateY(-1px);
    }

    .download-link-content {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      flex: 1;
    }

    .download-link-icon {
      font-size: 1.5rem;
      color: var(--primary-color);
    }

    .download-link-info {
      flex: 1;
    }

    .download-link-title {
      font-weight: 600;
      color: var(--primary-color);
      margin: 0 0 var(--spacing-xs) 0;
      font-size: 0.95rem;
    }

    .download-link-meta {
      font-size: 0.85rem;
      color: var(--text-muted);
      margin: 0;
    }

    .download-link-action {
      color: var(--primary-color);
      font-size: 0.9rem;
      font-weight: 500;
      padding: var(--spacing-xs) var(--spacing-sm);
      border: 1px solid var(--primary-color);
      border-radius: var(--border-radius);
      background: transparent;
      transition: all 0.2s ease;
    }

    .download-link:hover .download-link-action {
      background: var(--primary-color);
      color: white;
    }
    /* Key Header and Inline Add Styles */
    .key-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: var(--spacing-xs);
      /* Add padding to ensure space for delete button */
      padding-right: 35px;
    }

    .key-header .add-new {
      cursor: pointer;
      font-size: 1.2rem;
      color: var(--secondary-color);
      margin-left: var(--spacing-sm);
      transition: color 0.2s ease;
    }

    .key-header .add-new:hover {
      color: #0e8c65;
    }

    /* Category items */
    .category-item {
      position: relative;
      padding: var(--spacing-xs);
      font-size: 0.9rem;
      break-inside: avoid;
      page-break-inside: avoid;
      border-radius: var(--border-radius);
      transition: background-color 0.2s ease;
    }

    .category-item:hover {
      background-color: rgba(0, 75, 141, 0.05);
    }

    .category-item label {
      display: flex;
      align-items: flex-start;
      cursor: pointer;
      width: 100%;
    }

    .category-item input[type="checkbox"] {
      margin-right: var(--spacing-xs);
      margin-top: 3px;
      flex-shrink: 0;
    }

    .category-item .remove-btn {
      display: none;
      position: absolute;
      right: 4px;
      top: 4px;
      color: var(--danger-color);
      font-weight: bold;
      cursor: pointer;
      font-size: 1rem;
      line-height: 1;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      width: 20px;
      height: 20px;
      text-align: center;
      transition: all 0.2s ease;
    }

    .category-item:hover .remove-btn {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .category-item .remove-btn:hover {
      background: var(--danger-color);
      color: white;
    }

    /* New entry styles */
    .category-item.new-entry {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-xs);
      background-color: rgba(16, 163, 127, 0.1);
    }

    .category-item.new-entry input[type="text"] {
      flex: 1;
      padding: var(--spacing-xs) var(--spacing-sm);
      font-size: 0.9rem;
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      font-family: inherit;
    }

    .category-item.new-entry .cancel-new {
      color: var(--danger-color);
      cursor: pointer;
      font-size: 1.1rem;
      line-height: 1;
      transition: color 0.2s ease;
    }

    .category-item.new-entry .cancel-new:hover {
      color: #b71c1c;
    }

    .category-item.new-entry .ask-chatgpt-link {
      font-size: 0.85rem;
      text-decoration: none;
      color: var(--primary-color);
      white-space: nowrap;
      transition: color 0.2s ease;
    }

    .category-item.new-entry .ask-chatgpt-link:hover {
      text-decoration: underline;
      color: #003a6b;
    }

    /* Header Add Group Styles */
    .categories-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: var(--spacing-xs);
    }

    .categories-header .add-group {
      cursor: pointer;
      font-size: 1.2rem;
      color: var(--secondary-color);
      margin-left: var(--spacing-sm);
      transition: color 0.2s ease;
    }

    .categories-header .add-group:hover {
      color: #0e8c65;
    }

    .header-new-entry {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      margin-top: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .header-new-entry input[type="text"] {
      flex: 1;
      min-width: 200px;
      padding: var(--spacing-xs) var(--spacing-sm);
      font-size: 0.9rem;
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      font-family: inherit;
    }

    .header-new-entry .cancel-new {
      color: var(--danger-color);
      cursor: pointer;
      font-size: 1.1rem;
      line-height: 1;
      transition: color 0.2s ease;
    }

    .header-new-entry .cancel-new:hover {
      color: #b71c1c;
    }

    .header-new-entry .ask-chatgpt-link {
      font-size: 0.85rem;
      text-decoration: none;
      color: var(--primary-color);
      white-space: nowrap;
      transition: color 0.2s ease;
    }

    .header-new-entry .ask-chatgpt-link:hover {
      text-decoration: underline;
      color: #003a6b;
    }

    /* POPUP OVERLAY STYLES */
    .hidden-overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.6);
      justify-content: center;
      align-items: center;
      z-index: 1000;
      padding: var(--spacing-md);
    }

    .visible-overlay {
      display: flex;
    }

    /* Story Dialog */
    .story-dialog {
      background: #fff;
      border-radius: var(--border-radius-large);
      max-width: 600px;
      width: 100%;
      max-height: 90vh;
      padding: var(--spacing-lg);
      box-shadow: var(--shadow-medium);
      position: relative;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .story-header h2 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--primary-color);
      font-size: 1.5rem;
      padding-right: 40px;
    }

    .story-content {
      flex: 1;
      overflow-y: auto;
      font-family: inherit;
      font-size: 0.95rem;
      line-height: 1.6;
    }

    .story-content ul {
      padding-left: var(--spacing-lg);
      margin: 0;
    }

    .story-content li {
      margin-bottom: var(--spacing-sm);
    }

    .signature-block {
      margin-top: var(--spacing-lg);
      padding-top: var(--spacing-md);
      border-top: 1px solid #eee;
      font-weight: 500;
    }

    .signature-block a {
      display: block;
      margin-top: var(--spacing-xs);
      color: var(--primary-color);
      text-decoration: none;
      transition: color 0.2s ease;
    }

    .signature-block a:hover {
      text-decoration: underline;
      color: #003a6b;
    }

    .close-btn {
      position: absolute;
      top: var(--spacing-sm);
      right: var(--spacing-sm);
      background: transparent;
      border: none;
      font-size: 1.5rem;
      width: 30px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: var(--text-muted);
      cursor: pointer;
      border-radius: 50%;
      transition: all 0.2s ease;
    }

    .close-btn:hover {
      color: var(--text-color);
      background: rgba(0, 0, 0, 0.1);
    }

    /* INSTRUCTIONS POPUP STYLES */
    .instructions-dialog {
      background: #fff;
      border-radius: var(--border-radius-large);
      max-width: 95vw;
      width: 100%;
      max-width: 1200px;
      height: 85vh;
      max-height: 800px;
      padding: var(--spacing-lg);
      box-shadow: var(--shadow-medium);
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .instructions-header h2 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--primary-color);
      font-size: 1.5rem;
      padding-right: 40px;
    }

    .instructions-content {
      flex: 1;
      overflow: auto;
      border: 1px solid #ddd;
      border-radius: var(--border-radius);
      background: #fff;
    }

    .markdown-content {
      padding: var(--spacing-md);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #24292f;
      max-width: none;
    }

    .markdown-content h1 {
      font-size: 1.75rem;
      font-weight: 600;
      margin: 0 0 var(--spacing-md) 0;
      padding-bottom: var(--spacing-xs);
      border-bottom: 1px solid #d1d9e0;
      color: var(--primary-color);
    }

    .markdown-content h2 {
      font-size: 1.25rem;
      font-weight: 600;
      margin: var(--spacing-xl) 0 var(--spacing-md) 0;
      padding-bottom: var(--spacing-xs);
      border-bottom: 1px solid #d1d9e0;
      color: var(--primary-color);
    }
    .markdown-content h3 {
      font-size: 1.25rem;
      font-weight: 600;
      margin: 1.5rem 0 0.5rem 0;
      color: #004B8D;
    }
    .markdown-content h4 {
      font-size: 1rem;
      font-weight: 600;
      margin: 1rem 0 0.5rem 0;
      color: #004B8D;
    }
    .markdown-content p {
      margin: 0 0 1rem 0;
    }
    .markdown-content ul, .markdown-content ol {
      margin: 0 0 1rem 0;
      padding-left: 2rem;
    }
    .markdown-content li {
      margin: 0.25rem 0;
    }
    .markdown-content code {
      background: #f6f8fa;
      padding: 0.2rem 0.4rem;
      border-radius: 3px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
      font-size: 0.85rem;
    }
    .markdown-content pre {
      background: #f6f8fa;
      padding: 1rem;
      border-radius: 6px;
      overflow-x: auto;
      margin: 1rem 0;
    }
    .markdown-content pre code {
      background: none;
      padding: 0;
    }
    .markdown-content blockquote {
      border-left: 4px solid #d1d9e0;
      padding: 0 1rem;
      margin: 1rem 0;
      color: #656d76;
    }
    .markdown-content a {
      color: #0969da;
      text-decoration: none;
    }
    .markdown-content a:hover {
      text-decoration: underline;
    }

    /* Internal anchor links styling */
    .markdown-content a.internal-link {
      color: var(--primary-color);
      cursor: pointer;
      transition: color 0.2s ease;
    }

    .markdown-content a.internal-link:hover {
      color: #003a6b;
      text-decoration: underline;
    }

    /* Smooth scrolling for instructions content */
    .instructions-content {
      scroll-behavior: smooth;
    }
    .markdown-content strong {
      font-weight: 600;
    }
    .markdown-content em {
      font-style: italic;
    }
    .markdown-content hr {
      border: none;
      border-top: 1px solid #d1d9e0;
      margin: 2rem 0;
    }
    .loading-message {
      text-align: center;
      padding: 2rem;
      color: #656d76;
      font-style: italic;
    }
    .error-message {
      text-align: center;
      padding: 2rem;
      color: #d1242f;
      background: #fff8f8;
      border: 1px solid #f8d7da;
      border-radius: 4px;
      margin: 1rem;
    }

    /* ZIP code and coordinates styling */
    #coordsDisplay {
      font-family: Consolas, monospace;
      font-weight: 500;
      color: #004B8D;
    }

    .coords-info {
      color: #666;
    }

    .status-message {
      font-size: 0.9rem;
      margin-top: 5px;
      font-style: italic;
    }

    #radiusMiles {
      width: 60px;
    }

    #zipCode {
      width: 70px;
    }

    .chatgpt-link {
      margin-left: 10px;
    }

    #resetCategoriesLink {
      margin-left: 10px;
      color: #d32f2f;
      font-weight: 500;
    }

    #restoreGroupsLink {
      margin-left: 10px;
      color: #1976d2;
      font-weight: 500;
    }

    /* ========================================
       MAP PROVIDER & GOOGLE API KEY SETTINGS
       ======================================== */

    .settings-section {
      margin-top: var(--spacing-md);
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .map-provider-toggle {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .map-provider-toggle label {
      font-weight: 500;
      color: var(--text-color);
      margin-right: var(--spacing-sm);
    }

    .settings-section label {
      min-width: 100px;
      color: var(--text-color);
      font-weight: 500;
    }

    #googleApiKeyInput {
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      padding: var(--spacing-xs);
      background: var(--background-light);
      font-family: inherit;
      width: 200px;
    }

    #toggleGoogleKeyVisibility {
      background: transparent;
      border: none;
      cursor: pointer;
      font-size: 1rem;
      padding: var(--spacing-xs);
      border-radius: var(--border-radius);
      transition: background-color 0.2s ease;
    }

    #toggleGoogleKeyVisibility:hover {
      background: rgba(0, 0, 0, 0.1);
    }

    .status-message {
      font-size: 0.9rem;
      margin-left: var(--spacing-sm);
      font-style: italic;
    }

    .status-message.success {
      color: var(--success-color);
    }

    .status-message.danger {
      color: var(--danger-color);
    }

    .status-message.muted {
      color: var(--text-muted);
    }

    /* ========================================
       RESPONSIVE MEDIA QUERIES
       ======================================== */

    /* Responsive adjustments for settings sections */
    @media screen and (max-width: 600px) {
      .settings-section {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
      }

      .map-provider-toggle {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
      }

      .settings-section label {
        min-width: auto;
      }

      #googleApiKeyInput {
        width: 100%;
        max-width: 250px;
      }
    }

    /* Very small screens - 157px width (smartwatch/mini displays) */
    @media screen and (max-width: 157px) {
      body {
        padding: var(--spacing-xs);
        font-size: 0.75rem;
      }

      .branded-header {
        padding: var(--spacing-sm);
      }

      .brand-logo {
        font-size: 1.5rem;
      }

      .brand-tagline {
        font-size: 0.75rem;
      }

      .header-nav a {
        font-size: 0.8rem;
        padding: var(--spacing-xs);
      }

      .header-contact {
        font-size: 0.75rem;
      }

      h1 {
        font-size: 1rem;
        line-height: 1.2;
      }

      .categories-container {
        padding: var(--spacing-xs);
        overflow: hidden;
      }

      .key-groups-container {
        flex-direction: column;
        gap: var(--spacing-xs);
        width: 100%;
      }

      .key-group {
        width: 100%;
        min-width: 0;
        max-width: 100%;
      }

      .key-items.columns-2,
      .key-items.columns-3 {
        grid-template-columns: 1fr;
      }

      .actions {
        gap: var(--spacing-xs);
      }

      button {
        padding: var(--spacing-xs);
        font-size: 0.8rem;
      }

      #log {
        height: 150px;
        font-size: 0.7rem;
        padding: var(--spacing-xs);
      }

      .story-dialog,
      .instructions-dialog {
        width: 95vw;
        height: 95vh;
        padding: var(--spacing-sm);
      }

      .markdown-content {
        padding: var(--spacing-sm);
        font-size: 0.8rem;
      }
    }

    /* Small mobile screens - 360x740 (Galaxy S8/S9) */
    @media screen and (min-width: 360px) and (max-width: 390px) {
      body {
        padding: var(--spacing-sm);
      }

      .header-container {
        grid-template-columns: 1fr auto;
        gap: var(--spacing-sm);
      }

      .header-contact {
        display: none; /* Hide contact on small screens */
      }

      .categories-container {
        overflow: hidden;
      }

      .key-groups-container {
        flex-direction: column;
        width: 100%;
      }

      .key-group {
        width: 100%;
        min-width: 0;
        max-width: 100%;
      }

      .key-items.columns-2 {
        grid-template-columns: repeat(2, 1fr);
      }

      .key-items.columns-3 {
        grid-template-columns: repeat(2, 1fr);
      }

      .actions {
        flex-direction: row;
        flex-wrap: wrap;
      }

      .story-dialog {
        width: 95vw;
        max-height: 85vh;
      }

      .instructions-dialog {
        width: 95vw;
        height: 85vh;
      }
    }

    /* iPhone 12/13/14 - 390x844 */
    @media screen and (min-width: 390px) and (max-width: 430px) {
      .categories-container {
        overflow: hidden;
      }

      .key-groups-container {
        flex-direction: column;
        width: 100%;
      }

      .key-group {
        width: 100%;
        min-width: 0;
        max-width: 100%;
      }

      .key-items.columns-3 {
        grid-template-columns: repeat(2, 1fr);
      }

      .actions {
        flex-direction: row;
        gap: var(--spacing-sm);
      }

      .story-dialog {
        width: 90vw;
      }

      .instructions-dialog {
        width: 90vw;
        height: 80vh;
      }
    }

    /* iPhone 14 Pro Max - 430x932 */
    @media screen and (min-width: 430px) and (max-width: 480px) {
      .categories-container {
        overflow: hidden;
      }

      .key-groups-container {
        flex-direction: column;
        width: 100%;
      }

      .key-group {
        width: 100%;
        min-width: 0;
        max-width: 100%;
      }

      .key-items.columns-3 {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    /* Transition breakpoint - 480px to 600px (single column safer) */
    @media screen and (min-width: 480px) and (max-width: 600px) {
      body {
        padding: var(--spacing-md);
      }

      .categories-container {
        overflow: hidden;
      }

      .key-groups-container {
        flex-direction: column;
        gap: var(--spacing-sm);
        width: 100%;
      }

      .key-group {
        width: 100%;
        min-width: 0;
        max-width: 100%;
      }

      .key-items.columns-3 {
        grid-template-columns: repeat(2, 1fr);
      }

      .actions {
        flex-direction: row;
        justify-content: flex-start;
      }

      .story-dialog {
        width: 85vw;
        max-width: 600px;
      }

      .instructions-dialog {
        width: 85vw;
        max-width: 700px;
      }
    }

    /* Small tablets and large phones landscape - 600px to 768px */
    @media screen and (min-width: 600px) and (max-width: 767px) {
      body {
        padding: var(--spacing-md);
      }

      .categories-container {
        overflow: hidden;
      }

      .key-groups-container {
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--spacing-sm);
        width: 100%;
      }

      .key-group {
        /* Allow groups to size based on content, but limit to reasonable widths */
        width: fit-content;
        min-width: 250px;
        max-width: calc(50% - var(--spacing-sm) / 2);
        flex: 0 1 auto;
      }

      .key-items.columns-3 {
        grid-template-columns: repeat(3, 1fr);
      }

      .actions {
        flex-direction: row;
        justify-content: flex-start;
      }

      .story-dialog {
        width: 85vw;
        max-width: 600px;
      }

      .instructions-dialog {
        width: 85vw;
        max-width: 700px;
      }
    }

    /* Tablets - 768x1024 (iPad) */
    @media screen and (min-width: 768px) and (max-width: 1023px) {
      body {
        padding: var(--spacing-lg);
      }

      .header-contact {
        display: block; /* Show contact info on tablets and up */
      }

      .categories-container {
        overflow: hidden;
      }

      .key-groups-container {
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--spacing-md);
        width: 100%;
      }

      .key-group {
        /* Allow groups to size based on content, but limit to reasonable widths */
        width: fit-content;
        min-width: 280px;
        max-width: calc(50% - var(--spacing-md) / 2);
        flex: 0 1 auto;
      }

      .actions {
        flex-direction: row;
        justify-content: flex-start;
        gap: var(--spacing-md);
      }

      .story-dialog {
        width: 80vw;
        max-width: 700px;
      }

      .instructions-dialog {
        width: 85vw;
        max-width: 900px;
        height: 85vh;
      }

      #log {
        height: 250px;
      }
    }

    /* Large tablets landscape - 820x1180 */
    @media screen and (min-width: 820px) and (max-width: 1023px) {
      .categories-container {
        overflow: hidden;
      }

      .key-groups-container {
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--spacing-md);
        width: 100%;
      }

      .key-group {
        /* Allow groups to size based on content, but limit to reasonable widths */
        width: fit-content;
        min-width: 250px;
        max-width: calc(33.333% - var(--spacing-md) * 2 / 3);
        flex: 0 1 auto;
      }

      .instructions-dialog {
        width: 90vw;
        max-width: 1000px;
      }
    }

    /* Desktop and large screens - 1024x1366+ */
    @media screen and (min-width: 1024px) {
      body {
        padding: var(--spacing-xl);
        max-width: 1400px;
        margin: 0 auto;
      }

      /* Header styles already defined in base responsive rules */

      .categories-container {
        overflow: hidden;
      }

      .key-groups-container {
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--spacing-lg);
        width: 100%;
      }

      .key-group {
        /* Allow groups to size based on content, but limit to reasonable widths */
        width: fit-content;
        min-width: 300px;
        max-width: calc(33.333% - var(--spacing-lg) * 2 / 3);
        flex: 0 1 auto;
      }

      .key-items.columns-3 {
        grid-template-columns: repeat(3, 1fr);
      }

      .actions {
        flex-direction: row;
        gap: var(--spacing-lg);
      }

      .story-dialog {
        width: 70vw;
        max-width: 800px;
      }

      .instructions-dialog {
        width: 80vw;
        max-width: 1200px;
        height: 80vh;
      }

      #log {
        height: 300px;
      }
    }

    /* Large desktop screens - 1368px+ */
    @media screen and (min-width: 1368px) {
      .categories-container {
        overflow: hidden;
      }

      .key-groups-container {
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--spacing-lg);
        width: 100%;
      }

      .key-group {
        /* Allow groups to size based on content, but limit to reasonable widths */
        width: fit-content;
        min-width: 280px;
        max-width: calc(25% - var(--spacing-lg) * 3 / 4);
        flex: 0 1 auto;
      }

      .instructions-dialog {
        width: 75vw;
      }
    }

    /* Landscape orientation adjustments */
    @media screen and (orientation: landscape) and (max-height: 500px) {
      .story-dialog,
      .instructions-dialog {
        height: 90vh;
        max-height: none;
      }

      .story-content,
      .instructions-content {
        max-height: 60vh;
      }

      #log {
        height: 150px;
      }
    }

    /* High DPI displays */
    @media screen and (-webkit-min-device-pixel-ratio: 2),
           screen and (min-resolution: 192dpi) {
      .markdown-content {
        font-size: 0.9rem;
      }

      button {
        padding: calc(var(--spacing-sm) + 1px) calc(var(--spacing-md) + 2px);
      }
    }

    /* Page Header Styles */
    .page-header {
      background: linear-gradient(135deg, var(--background-light) 0%, #f0f8ff 100%);
      border: 1px solid var(--light-border);
      border-radius: var(--border-radius-large);
      padding: var(--spacing-lg) var(--spacing-md);
      margin-bottom: var(--spacing-lg);
      box-shadow: var(--shadow-light);
    }

    .page-header h2 {
      color: var(--primary-color);
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0 0 var(--spacing-md) 0;
      text-align: center;
    }

    .page-header .page-description {
      font-size: 1rem;
      line-height: 1.6;
      color: var(--text-light);
      text-align: center;
      margin: 0;
    }

    .page-header .page-description a {
      color: var(--primary-color);
      text-decoration: none;
      transition: color 0.2s ease;
    }

    .page-header .page-description a:hover {
      color: #003a6b;
      text-decoration: underline;
    }

    .page-controls {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
      align-items: center;
      margin-top: var(--spacing-md);
      padding-top: var(--spacing-md);
      border-top: 1px solid var(--light-border);
    }

    .page-controls .control-group {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-sm);
      align-items: center;
      justify-content: center;
    }

    .page-controls input[type="number"],
    .page-controls input[type="text"] {
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      padding: var(--spacing-xs) var(--spacing-sm);
      font-family: inherit;
      font-size: 0.9rem;
    }

    .page-controls .coords-info {
      font-size: 0.85rem;
      color: var(--text-muted);
      text-align: center;
      margin-top: var(--spacing-xs);
    }

    .page-controls .action-links {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-sm);
      justify-content: center;
      margin-top: var(--spacing-sm);
    }

    .page-controls .action-links a {
      font-size: 0.9rem;
      color: var(--primary-color);
      text-decoration: none;
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--border-radius);
      transition: all 0.2s ease;
      border: 1px solid transparent;
    }

    .page-controls .action-links a:hover {
      background: rgba(0, 75, 141, 0.1);
      border-color: var(--primary-color);
      text-decoration: none;
    }

    .page-controls .action-links a#resetCategoriesLink {
      color: var(--danger-color);
    }

    .page-controls .action-links a#resetCategoriesLink:hover {
      background: rgba(211, 47, 47, 0.1);
      border-color: var(--danger-color);
    }

    .page-controls .action-links a#restoreGroupsLink {
      color: var(--success-color);
    }

    .page-controls .action-links a#restoreGroupsLink:hover {
      background: rgba(25, 118, 210, 0.1);
      border-color: var(--success-color);
    }

    /* Footer Styles */
    footer {
      margin-top: var(--spacing-xl);
      padding: var(--spacing-lg) var(--spacing-sm);
      background: linear-gradient(135deg, var(--background-light) 0%, var(--background-lighter) 100%);
      border-top: 2px solid var(--light-border);
      border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
      box-shadow: var(--shadow-light);
    }

    .footer-settings {
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-lg);
      border-bottom: 1px solid var(--light-border);
    }

    .footer-settings h3 {
      color: var(--primary-color);
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0 0 var(--spacing-md) 0;
      text-align: center;
    }

    .footer-content {
      max-width: 1200px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: 1fr;
      gap: var(--spacing-md);
      text-align: center;
    }

    .footer-section h3 {
      color: var(--primary-color);
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0 0 var(--spacing-sm) 0;
      border-bottom: 1px solid var(--light-border);
      padding-bottom: var(--spacing-xs);
    }

    .footer-section p,
    .footer-section ul {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: 0.9rem;
      line-height: 1.5;
      color: var(--text-light);
    }

    .footer-section ul {
      list-style: none;
      padding: 0;
    }

    .footer-section ul li {
      margin: var(--spacing-xs) 0;
    }

    .footer-section a {
      color: var(--primary-color);
      text-decoration: none;
      transition: color 0.2s ease;
    }

    .footer-section a:hover {
      color: #003a6b;
      text-decoration: underline;
    }

    .footer-bottom {
      margin-top: var(--spacing-md);
      padding-top: var(--spacing-md);
      border-top: 1px solid var(--light-border);
      text-align: center;
      font-size: 0.85rem;
      color: var(--text-muted);
    }

    .footer-bottom p {
      margin: 0;
    }

    /* Results Container responsive styles */
    @media screen and (min-width: 600px) {
      .downloads-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .results-header {
        flex-direction: row;
      }

      .results-actions {
        flex-direction: row;
        gap: var(--spacing-sm);
      }
    }

    /* Mobile responsive styles for download button */
    @media screen and (max-width: 600px) {
      .download-all-files-btn {
        font-size: 0.85rem;
        padding: var(--spacing-xs);
        margin-right: 0;
        margin-bottom: var(--spacing-xs);
        width: 100%;
        text-align: center;
      }

      .results-actions {
        flex-direction: column;
        gap: var(--spacing-xs);
      }
    }

    @media screen and (min-width: 1024px) {
      .downloads-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    /* Page Header responsive styles */
    @media screen and (min-width: 600px) {
      .page-header h2 {
        font-size: 1.75rem;
      }

      .page-controls {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
      }

      .page-controls .control-group {
        flex-direction: row;
      }
    }

    @media screen and (min-width: 768px) {
      .page-header {
        padding: var(--spacing-xl) var(--spacing-lg);
      }

      .page-header h2 {
        font-size: 2rem;
      }

      .page-header .page-description {
        font-size: 1.1rem;
      }
    }

    @media screen and (min-width: 1024px) {
      .page-header h2 {
        font-size: 2.25rem;
      }
    }

    /* Footer responsive styles */
    @media screen and (min-width: 600px) {
      .footer-settings {
        text-align: left;
      }

      .footer-settings .settings-section {
        flex-direction: row;
        align-items: center;
        gap: var(--spacing-md);
      }

      .footer-content {
        grid-template-columns: repeat(2, 1fr);
        text-align: left;
      }
    }

    @media screen and (min-width: 768px) {
      footer {
        padding: var(--spacing-xl) var(--spacing-md);
      }

      .footer-content {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-lg);
      }
    }

    @media screen and (min-width: 1024px) {
      .footer-content {
        grid-template-columns: repeat(4, 1fr);
      }
    }

    /* Print styles */
    @media print {
      .branded-header,
      .page-header,
      .actions,
      .results-container,
      .hidden-overlay,
      .visible-overlay,
      footer {
        display: none !important;
      }

      body {
        padding: 0;
        font-size: 12pt;
        line-height: 1.4;
      }

      .categories-container {
        break-inside: avoid;
      }

      .key-group {
        break-inside: avoid;
        margin-bottom: 1rem;
      }

      #log {
        height: auto;
        max-height: 200px;
        font-size: 10pt;
      }
    }
  </style>
  <!-- Add JSZip library in the head section -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
  <!-- Add marked.js library for markdown parsing -->
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>
  <!-- STORY OF PAGE POPUP -->
  <div id="pageStoryOverlay" class="hidden-overlay">
    <div id="pageStoryDialog" class="story-dialog" tabindex="-1">
      <button id="closePageStory" class="close-btn" aria-label="Close">&times;</button>
      <header class="story-header">
        <h2>The Story of This Page</h2>
      </header>
      <div class="story-content">
        <ul>
          <li>This page was originally described as a <a href="https://chatgpt.com/share/683fae9c-f324-8013-b4ba-b9b978770b26" target="_blank">ChatGPT prompt</a>.</li>
          <li>ChatGPT generated a suitable Python program to accomplish the task, but to avoid Python dependencies, the reply was refactored as an HTML page.  This allows anyone to use the page without needing to install Python.</li>
          <li>Again, to avoid Google Dependencies, the mapping engine was switched from Google Maps to OpenStreetMap.  This allows for anyone to use the page without needing a Google API key.</li>
          <li>The resulting code was copy-and-pasted into VS Code and opened with <a href="https://www.augmentcode.com/" target="_blank">Augment</a>.</li>
          <li>Within VS Code + Augment, this project was massaged and refactored multiple times.  Ultimately, the use of the Augment AI Agent is the best way to '<i>vibe code</i>.'</li>
          <li>The code was also pushed to GitHub: <a href="https://github.com/mytech-today-now/business_search" target="_blank">mytech-today-now/business_search</a>.</li>
          <li>A separate ChatGPT prompt produced 35 business types that benefit from MSP services in Chicagoland; the reply was reformatted as a comma-separated list and re-fed into ChatGPT/Augment.</li>
          <li>On June 1, 2025, the code was refactored 29 times using Augment (50 total prompts to generate and refine code). Additional prompts refined and improved the final version.</li>
          <li>Finally, the entire codebase was fed back into ChatGPT for further prompt generation, then re-applied in Augment. This iterative AI-driven workflow has been an extremely effective way to "vibe code.  Currently, June 5, 2025, Augment does a better job of 'vibe coding' than ChatGPT with Codex"</li>
          <li>By comparison, 15 years ago I wrote a similar program in C# over the course of four weeks. This AI version took 1.5 days with almost zero manual effort, and is much more professional, comphrensive, interactive, and responsive.</li>
        </ul>
        <div class="signature-block">
          Kyle Rode
          <a href="https://mytech.today" target="_blank">myTech.Today</a>
          <a href="tel:***********" target="_blank">(*************</a>
          <a href="mailto:<EMAIL>" target="_blank"><EMAIL></a>
        </div>
      </div>
    </div>
  </div>

  <!-- INSTRUCTIONS POPUP -->
  <div id="instructionsOverlay" class="hidden-overlay">
    <div id="instructionsDialog" class="instructions-dialog" tabindex="-1">
      <button id="closeInstructions" class="close-btn" aria-label="Close">&times;</button>
      <header class="instructions-header">
        <h2>Instructions & Documentation</h2>
      </header>
      <div class="instructions-content">
        <div id="instructionsContent" class="markdown-content">
          <div class="loading-message">Loading documentation...</div>
        </div>
      </div>
    </div>
  </div>

  <header class="branded-header">
    <div class="header-container">
      <div class="header-brand">
        <h1 class="brand-logo">🏢 myTech.Today</h1>
        <p class="brand-tagline">Professional MSP Services • Barrington, IL • (*************</p>
      </div>

      <nav class="header-nav">
        <a href="#" id="openInstructions">📖 Instructions</a>
        <a href="#" id="openPageStory">📜 Story</a>
      </nav>

      <div class="header-contact">
        <a href="https://mytech.today" target="_blank" rel="noopener noreferrer">mytech.today</a>
        <a href="mailto:<EMAIL>?subject=Business%20Search%20Tool%20Inquiry"><EMAIL></a>
      </div>
    </div>
  </header>

  <!-- Page Header Section -->
  <section class="page-header">
    <h2>Export Businesses (<span id="radiusDisplay">30</span> mi radius around <span id="zipDisplay">60010</span>) as CSV</h2>
    <p class="page-description">
      This page uses the Overpass API (<a href="https://www.openstreetmap.org/about" target="_blank" rel="noopener noreferrer">OpenStreetMap</a>) to fetch business data. Select categories below and click "Run Export" to generate CSVs. No API key is required for basic functionality.
    </p>

    <div class="page-controls">
      <div class="control-group">
        <label for="radiusMiles">Search radius:</label>
        <input type="number" id="radiusMiles" value="30" min="1" max="100" title="Search radius in miles"> mi
        (<span id="radiusMeters">48280</span>m)
      </div>

      <div class="control-group">
        <label for="zipCode">around ZIP:</label>
        <input type="text" id="zipCode" value="60010" pattern="[0-9]{5}" maxlength="5" title="Enter a 5-digit ZIP code and press Enter or click away to update coordinates">
      </div>

      <div class="coords-info">
        Current coordinates: <span id="coordsDisplay">42.1543, -88.1362</span>
      </div>

      <div class="action-links">
        <a href="#" id="askChatGPTLink" target="_blank" rel="noopener noreferrer">Ask ChatGPT for OSM tags</a>
        <a href="#" id="resetCategoriesLink">Reset Categories</a>
        <a href="#" id="restoreGroupsLink">Restore Hidden Groups</a>
      </div>
    </div>
  </section>

  <main>

  <div class="categories-container">
    <div class="categories-header">
      <label><input type="checkbox" id="selectAllCategories"> Select/Unselect All Categories</label>
      <span class="add-group" title="Add New Group">+</span>
    </div>
    <div id="headerNewEntryContainer"></div>
    <div class="category-items" id="allCategories"></div>
  </div>

  <div class="actions">
    <button id="runBtn">Run Export</button>
    <button id="exportConfigBtn">Export Config</button>
    <button id="importConfigBtn">Import Config</button>
    <input
      type="file"
      id="importConfigInput"
      accept=".json"
      style="display: none;"
    />
  </div>

  <div id="log"></div>

  <!-- Results Container -->
  <div id="resultsContainer" class="results-container">
    <div class="results-header">
      <h3>📥 Export Results</h3>
      <div class="results-actions">
        <button type="button" id="downloadAllFilesBtn" class="download-all-files-btn" disabled>Download all 0 files as a zip file</button>
        <button type="button" id="clearResultsBtn" class="clear-results-btn">Clear All</button>
      </div>
    </div>
    <div id="downloads" class="downloads-grid"></div>
  </div>
  </main>

  <footer>
    <!-- Map Provider & API Settings Section -->
    <div class="footer-settings">
      <h3>⚙️ Map Provider Settings</h3>

      <div id="mapProviderContainer" class="settings-section">
        <label>Map Provider:</label>
        <div class="map-provider-toggle">
          <input type="radio" name="mapProvider" id="osmProvider" value="osm" checked>
          <label for="osmProvider">OpenStreetMap</label>
          <input type="radio" name="mapProvider" id="googleProvider" value="google">
          <label for="googleProvider">Google Maps</label>
        </div>
      </div>

      <div id="googleKeyContainer" class="settings-section">
        <label for="googleApiKeyInput">Google API Key:</label>
        <input type="password" id="googleApiKeyInput" placeholder="Enter API key" maxlength="50">
        <button type="button" id="toggleGoogleKeyVisibility" title="Show/Hide Key">👁️</button>
        <button type="button" id="saveGoogleKeyBtn">Save Key</button>
        <button type="button" id="removeGoogleKeyBtn" disabled>Remove Key</button>
        <span id="googleKeyStatus" class="status-message"></span>
      </div>
    </div>

    <div class="footer-content">
      <div class="footer-section">
        <h3>About This Tool</h3>
        <p>Export business data using OpenStreetMap's Overpass API. No API key required for basic functionality.</p>
        <ul>
          <li><a href="#" id="openInstructionsFooter">📖 Instructions</a></li>
          <li><a href="#" id="openPageStoryFooter">📜 Development Story</a></li>
          <li><a href="https://github.com/mytech-today-now/business_search" target="_blank" rel="noopener noreferrer">💻 Source Code</a></li>
        </ul>
      </div>

      <div class="footer-section">
        <h3>Data Sources</h3>
        <p>Powered by open data and APIs</p>
        <ul>
          <li><a href="https://www.openstreetmap.org/about" target="_blank" rel="noopener noreferrer">🗺️ OpenStreetMap</a></li>
          <li><a href="https://overpass-api.de/" target="_blank" rel="noopener noreferrer">🔍 Overpass API</a></li>
          <li><a href="https://developers.google.com/maps/documentation/places/web-service" target="_blank" rel="noopener noreferrer">📍 Google Places API</a></li>
          <li><a href="https://nominatim.openstreetmap.org/" target="_blank" rel="noopener noreferrer">📮 Nominatim Geocoding</a></li>
        </ul>
      </div>

      <div class="footer-section">
        <h3>Technology Stack</h3>
        <p>Built with modern web technologies</p>
        <ul>
          <li>🌐 HTML5 & CSS3</li>
          <li>⚡ Vanilla JavaScript</li>
          <li>📱 Responsive Design</li>
          <li>💾 Local Storage</li>
          <li>📦 JSZip Library</li>
          <li>📝 Marked.js (Markdown)</li>
        </ul>
      </div>

      <div class="footer-section">
        <h3>Contact & Support</h3>
        <p>Professional MSP services in Chicagoland</p>
        <ul>
          <li><a href="https://mytech.today" target="_blank" rel="noopener noreferrer">🏢 myTech.Today</a></li>
          <li><a href="tel:***********" target="_blank" rel="noopener noreferrer">📞 (*************</a></li>
          <li><a href="mailto:<EMAIL>?subject=Business%20Search%20Tool%20Inquiry"> <EMAIL></a></li>
          <li>📍 Barrington, IL</li>
        </ul>
      </div>
    </div>

    <div class="footer-bottom">
      <p>© 2025 myTech.Today. All rights reserved. | Built with ❤️ using AI-assisted development</p>
      <p><a href="https://www.augmentcode.com/" target="_blank" rel="noopener noreferrer">Powered by Augment</a>, <a href="https://chat.openai.com/" target="_blank" rel="noopener noreferrer">ChatGPT</a>, <a href="https://code.visualstudio.com/" target="_blank" rel="noopener noreferrer">VS Code</a> &amp; <a href="https://github.com/mytech-today-now/business_search" target="_blank" rel="noopener noreferrer">GitHub</a></p>
    </div>
  </footer>

  <script>
    // ======================================================
    // Global Map Provider & Google API Key Management
    // ======================================================
    // Initialize map settings from localStorage - make these global
    let USE_GOOGLE_MAPS = false;
    let googleApiKey = null;

    (function () {

      // ======================================================
      // User Categories Storage Helpers
      // ======================================================
      function loadUserCategories() {
        try {
          const stored = localStorage.getItem("userCategories");
          if (!stored) return {};
          return JSON.parse(stored) || {};
        } catch (err) {
          console.error("Error loading user categories:", err);
          return {};
        }
      }
      
      function saveUserCategories(userCategories) {
        try {
          localStorage.setItem("userCategories", JSON.stringify(userCategories));
          return true;
        } catch (err) {
          console.error("Error saving user categories:", err);
          return false;
        }
      }

      // ======================================================
      // User Groups Storage Helpers
      // ======================================================
      function loadUserGroups() {
        try {
          const stored = localStorage.getItem("userGroups");
          if (!stored) return [];
          return JSON.parse(stored) || [];
        } catch (err) {
          console.error("Error loading user groups:", err);
          return [];
        }
      }

      function saveUserGroups(userGroups) {
        try {
          localStorage.setItem("userGroups", JSON.stringify(userGroups));
          return true;
        } catch (err) {
          console.error("Error saving user groups:", err);
          return false;
        }
      }
      
      // ======================================================
      // Map Settings Initialization
      // ======================================================
      // Initialize map settings from localStorage - make this global
      window.initializeMapSettings = function initializeMapSettings() {
        // Read map provider from localStorage
        const storedProvider = localStorage.getItem("mapProvider");
        if (storedProvider === "google") {
          USE_GOOGLE_MAPS = true;
          document.getElementById("googleProvider").checked = true;
        } else {
          USE_GOOGLE_MAPS = false;
          document.getElementById("osmProvider").checked = true;
        }

        // Read Google API key from localStorage
        const storedKey = localStorage.getItem("googleApiKey");
        if (storedKey) {
          googleApiKey = storedKey;
          window.obscureKeyInInput();
          document.getElementById("removeGoogleKeyBtn").disabled = false;
          document.getElementById("saveGoogleKeyBtn").textContent = "Update Key";
          const statusEl = document.getElementById("googleKeyStatus");
          statusEl.textContent = "Key saved";
          statusEl.className = "status-message success";
        }

        console.log("Map provider initialized:", USE_GOOGLE_MAPS ? "Google" : "OpenStreetMap");
      }

      // Helper function to obscure API key in input - make this global
      window.obscureKeyInInput = function obscureKeyInInput() {
        const input = document.getElementById("googleApiKeyInput");
        input.value = "•".repeat(googleApiKey.length);
        input.setAttribute("data-obscured", "true");
        input.type = "password";
      }

      // Define log function globally first so it can be used by fetchGooglePlaces
      window.log = function log(message) {
        const timeStamp = new Date().toLocaleTimeString();
        const logDiv = document.getElementById("log");
        if (logDiv) {
          logDiv.textContent += `[${timeStamp}] ${message}\n`;
          logDiv.scrollTop = logDiv.scrollHeight;
        } else {
          console.log(`[${timeStamp}] ${message}`);
        }
      }

      // Enhanced Google Places API implementation with comprehensive error handling
      window.fetchGooglePlaces = async function fetchGooglePlaces(filters, lat, lng, radius) {
        if (!googleApiKey) {
          console.warn("Google API Key missing. Cannot fetch Google Places.");
          window.log("[!] Google API key not found; switching back to OpenStreetMap.");
          return { success: false, error: "No API key", results: [] };
        }

        const filterStr = filters.map(f => `${f.key}:${f.value}`).join(", ");
        window.log(`→ (Google) Searching for ${filterStr} around ${lat},${lng} radius ${radius}m`);

        try {
          // Validate input parameters
          if (!lat || !lng || isNaN(lat) || isNaN(lng)) {
            throw new Error("Invalid coordinates provided");
          }

          if (!radius || radius <= 0 || radius > 50000) {
            throw new Error("Invalid radius (must be between 1-50000 meters)");
          }

          // Convert OSM filters to Google Places search terms
          const searchQuery = convertOSMFiltersToGoogleQuery(filters);
          window.log(`   → Converted to Google query: "${searchQuery}"`);

          // Prepare API request parameters
          const apiUrl = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';
          const params = new URLSearchParams({
            location: `${lat},${lng}`,
            radius: radius.toString(),
            keyword: searchQuery,
            key: googleApiKey
          });

          window.log(`   → Making API request to Google Places...`);
          console.log(`Google Places API URL: ${apiUrl}?${params.toString().replace(googleApiKey, 'API_KEY_HIDDEN')}`);

          // Make the API request
          const response = await fetch(`${apiUrl}?${params.toString()}`);

          // Log response status
          window.log(`   → API Response Status: ${response.status} ${response.statusText}`);

          if (!response.ok) {
            // Handle specific HTTP error codes
            switch (response.status) {
              case 400:
                throw new Error("Bad Request - Invalid parameters sent to Google Places API");
              case 401:
                throw new Error("Unauthorized - Invalid API key");
              case 403:
                throw new Error("Forbidden - API key may be restricted or quota exceeded");
              case 429:
                throw new Error("Too Many Requests - API quota exceeded, try again later");
              case 500:
                throw new Error("Internal Server Error - Google Places API is experiencing issues");
              default:
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
          }

          const data = await response.json();

          // Check for API-specific errors in the response
          if (data.status && data.status !== 'OK') {
            let errorMessage = `Google Places API Error: ${data.status}`;

            switch (data.status) {
              case 'ZERO_RESULTS':
                window.log(`   → No results found for "${searchQuery}" in this area`);
                return { success: true, error: null, results: [], message: "No results found" };
              case 'OVER_QUERY_LIMIT':
                errorMessage = "API quota exceeded. Please check your billing settings.";
                break;
              case 'REQUEST_DENIED':
                errorMessage = "Request denied. Check API key permissions and restrictions.";
                break;
              case 'INVALID_REQUEST':
                errorMessage = "Invalid request parameters.";
                break;
              case 'UNKNOWN_ERROR':
                errorMessage = "Unknown server error. Please try again.";
                break;
            }

            if (data.error_message) {
              errorMessage += ` Details: ${data.error_message}`;
            }

            throw new Error(errorMessage);
          }

          // Process successful results
          const results = data.results || [];
          window.log(`   → Found ${results.length} places`);

          if (results.length === 0) {
            window.log(`   → No businesses found matching "${searchQuery}"`);
            return { success: true, error: null, results: [], message: "No results found" };
          }

          // Convert Google Places results to our CSV format
          const csvRows = await convertGooglePlacesToCSV(results, lat, lng);
          window.log(`   → Converted ${csvRows.length} results to CSV format`);

          // Generate download link
          const filename = generateFilename(filters);
          generateDownloadLink(filename, csvRows);

          return {
            success: true,
            error: null,
            results: csvRows,
            message: `Successfully found ${results.length} places`
          };

        } catch (error) {
          // Use the enhanced error reporting function
          const errorReport = reportGoogleMapsError(error, 'Places API');

          return { success: false, error: errorReport.error, results: [] };
        }
      }

      // Helper function to convert OSM filters to Google Places search query
      function convertOSMFiltersToGoogleQuery(filters) {
        const queryTerms = [];

        for (const filter of filters) {
          // Map common OSM tags to Google Places search terms
          switch (filter.key) {
            case 'amenity':
              switch (filter.value) {
                case 'restaurant': queryTerms.push('restaurant'); break;
                case 'cafe': queryTerms.push('cafe'); break;
                case 'bank': queryTerms.push('bank'); break;
                case 'hospital': queryTerms.push('hospital'); break;
                case 'pharmacy': queryTerms.push('pharmacy'); break;
                case 'school': queryTerms.push('school'); break;
                case 'fuel': queryTerms.push('gas station'); break;
                case 'dentist': queryTerms.push('dentist'); break;
                case 'veterinary': queryTerms.push('veterinarian'); break;
                default: queryTerms.push(filter.value);
              }
              break;
            case 'shop':
              switch (filter.value) {
                case 'supermarket': queryTerms.push('grocery store'); break;
                case 'clothes': queryTerms.push('clothing store'); break;
                case 'electronics': queryTerms.push('electronics store'); break;
                case 'bakery': queryTerms.push('bakery'); break;
                case 'hairdresser': queryTerms.push('hair salon'); break;
                default: queryTerms.push(filter.value + ' store');
              }
              break;
            case 'office':
              queryTerms.push(filter.value + ' office');
              break;
            case 'tourism':
              switch (filter.value) {
                case 'hotel': queryTerms.push('hotel'); break;
                case 'attraction': queryTerms.push('tourist attraction'); break;
                default: queryTerms.push(filter.value);
              }
              break;
            default:
              // For unknown tags, use the value as-is
              queryTerms.push(filter.value);
          }
        }

        return queryTerms.join(' ');
      }

      // Helper function to convert Google Places results to CSV format
      async function convertGooglePlacesToCSV(places, searchLat, searchLng) {
        const csvRows = [];

        for (const place of places) {
          try {
            // Extract basic information
            const name = place.name || 'Unknown';
            const address = place.vicinity || place.formatted_address || 'Address not available';
            const lat = place.geometry?.location?.lat || '';
            const lng = place.geometry?.location?.lng || '';

            // Calculate distance from search center
            let distance = '';
            if (lat && lng && searchLat && searchLng) {
              const distanceKm = calculateDistance(searchLat, searchLng, lat, lng);
              distance = `${distanceKm.toFixed(2)} km`;
            }

            // Extract additional details
            const rating = place.rating || '';
            const priceLevel = place.price_level !== undefined ? '$'.repeat(place.price_level + 1) : '';
            const types = place.types ? place.types.join(', ') : '';
            const placeId = place.place_id || '';

            // Get phone number and website (requires additional API call)
            let phone = '';
            let website = '';

            // For now, we'll skip the additional details API call to avoid quota issues
            // In a production environment, you might want to make a Place Details API call here

            // Create CSV row matching the existing format
            const row = [
              name,                    // Business Name
              address,                 // Address
              lat,                     // Latitude
              lng,                     // Longitude
              distance,                // Distance
              phone,                   // Phone (empty for now)
              website,                 // Website (empty for now)
              types,                   // Business Types
              rating,                  // Rating
              priceLevel,              // Price Level
              placeId                  // Google Place ID
            ];

            csvRows.push(row);

          } catch (error) {
            console.warn(`Error processing place result:`, error, place);
            window.log(`   → Warning: Skipped one result due to data processing error`);
          }
        }

        return csvRows;
      }

      // Helper function to calculate distance between two coordinates
      function calculateDistance(lat1, lng1, lat2, lng2) {
        const R = 6371; // Earth's radius in kilometers
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
      }

      // Helper function to generate filename for Google Places results
      function generateFilename(filters) {
        const filterStr = filters.map(f => `${f.key}_${f.value}`).join('_');
        return `google_places_${filterStr}`.replace(/[^a-zA-Z0-9_]/g, '_');
      }

      // Helper function to generate download link for CSV data
      function generateDownloadLink(filename, csvRows) {
        try {
          // Add CSV header if not present
          const headerRow = [
            "Business Name", "Address", "Latitude", "Longitude", "Distance",
            "Phone", "Website", "Business Types", "Rating", "Price Level", "Place ID"
          ];

          const allRows = [headerRow, ...csvRows];
          const csvContent = generateCSVString(allRows);

          // Create blob and download link
          const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
          const url = URL.createObjectURL(blob);

          // Create enhanced download link element
          const downloadLink = document.createElement("a");
          downloadLink.href = url;
          downloadLink.download = `${filename}.csv`;
          downloadLink.className = "download-link";

          // Create the enhanced download link structure
          downloadLink.innerHTML = `
            <div class="download-link-content">
              <div class="download-link-icon">📊</div>
              <div class="download-link-info">
                <div class="download-link-title">${filename}.csv</div>
                <div class="download-link-meta">${csvRows.length} results • ${(blob.size / 1024).toFixed(1)} KB</div>
              </div>
            </div>
            <div class="download-link-action">Download</div>
          `;

          // Add to downloads section
          const downloadsDiv = document.getElementById("downloads");
          if (downloadsDiv) {
            downloadsDiv.appendChild(downloadLink);
            updateResultsContainer();
          }

          window.log(`   → Download link created: ${filename}.csv`);

        } catch (error) {
          console.error("Error generating download link:", error);
          window.log(`   [!] Error creating download link: ${error.message}`);
        }
      }

      // Helper function to update the results container visibility and download button
      window.updateResultsContainer = function updateResultsContainer() {
        const resultsContainer = document.getElementById("resultsContainer");
        const downloadsDiv = document.getElementById("downloads");
        const downloadAllBtn = document.getElementById("downloadAllFilesBtn");

        if (!resultsContainer || !downloadsDiv || !downloadAllBtn) return;

        const downloadLinks = downloadsDiv.querySelectorAll(".download-link");
        const count = downloadLinks.length;

        if (count > 0) {
          resultsContainer.classList.add("has-results");
          downloadAllBtn.textContent = `Download all ${count} file${count !== 1 ? 's' : ''} as a zip file`;
          // Keep button disabled until export is complete - will be enabled by enableDownloadAllButton()
        } else {
          resultsContainer.classList.remove("has-results");
          downloadAllBtn.textContent = "Download all 0 files as a zip file";
          downloadAllBtn.disabled = true;
        }
      }

      // Helper function to clear all results
      window.clearAllResults = function clearAllResults() {
        const downloadsDiv = document.getElementById("downloads");
        if (downloadsDiv) {
          downloadsDiv.innerHTML = "";
          updateResultsContainer();
          window.log("All download links cleared.");
        }
      }

      // Helper function to validate Google API key format
      function validateGoogleApiKey(apiKey) {
        if (!apiKey || typeof apiKey !== 'string') {
          return { valid: false, error: "API key is required" };
        }

        if (apiKey.length < 20) {
          return { valid: false, error: "API key is too short (minimum 20 characters)" };
        }

        if (apiKey.length > 100) {
          return { valid: false, error: "API key is too long (maximum 100 characters)" };
        }

        // Basic format check for Google API keys
        if (!/^[A-Za-z0-9_-]+$/.test(apiKey)) {
          return { valid: false, error: "API key contains invalid characters" };
        }

        return { valid: true, error: null };
      }

      // Enhanced error reporting function
      function reportGoogleMapsError(error, context = '') {
        const timestamp = new Date().toLocaleTimeString();
        console.error(`[${timestamp}] Google Maps Error${context ? ` (${context})` : ''}:`, error);

        // Categorize errors and provide helpful suggestions
        let suggestion = '';
        const errorMsg = error.message || error.toString();

        if (errorMsg.includes('API key')) {
          suggestion = 'Check your Google Cloud Console API key settings and restrictions';
        } else if (errorMsg.includes('quota') || errorMsg.includes('limit')) {
          suggestion = 'Check your Google Cloud billing and API quotas';
        } else if (errorMsg.includes('network') || errorMsg.includes('fetch') || errorMsg.includes('NetworkError')) {
          suggestion = 'Check your internet connection and firewall settings';
        } else if (errorMsg.includes('CORS')) {
          suggestion = 'This appears to be a browser security restriction';
        } else if (errorMsg.includes('timeout')) {
          suggestion = 'The request timed out - try again or check your connection';
        } else if (errorMsg.includes('Invalid request')) {
          suggestion = 'Check the search parameters and coordinates';
        }

        window.log(`   [!] ${errorMsg}`);
        if (suggestion) {
          window.log(`   → Suggestion: ${suggestion}`);
        }

        return { error: errorMsg, suggestion };
      }

      // Function to test Google API key validity - make it globally accessible
      window.testGoogleApiKey = async function testGoogleApiKey(apiKey) {
        try {
          window.log("   → Testing Google API key validity...");

          // Make a minimal test request to verify the API key works
          const testUrl = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';
          const testParams = new URLSearchParams({
            location: '42.1543,-88.1362', // Barrington, IL coordinates
            radius: '1000',
            key: apiKey
          });

          const response = await fetch(`${testUrl}?${testParams.toString()}`);
          const data = await response.json();

          if (response.ok && data.status === 'OK') {
            window.log("   ✓ API key is valid and working");
            return { valid: true, error: null };
          } else if (data.status === 'REQUEST_DENIED') {
            return { valid: false, error: "API key is invalid or restricted" };
          } else if (data.status === 'OVER_QUERY_LIMIT') {
            return { valid: false, error: "API quota exceeded" };
          } else {
            return { valid: false, error: data.error_message || `API returned status: ${data.status}` };
          }

        } catch (error) {
          return { valid: false, error: `Network error: ${error.message}` };
        }
      }

      // ======================================================
      // Configuration
      // ======================================================
      // Default coordinates for ZIP 60010 (Barrington, IL) - make global
      window.LAT = 42.1543;
      window.LNG = -88.1362;
      window.ZIP_CODE = "60010";

      // Default radius: 30 miles - make global
      const MILES_TO_METERS = 1609.34;
      let RADIUS_MILES = 30;
      window.RADIUS = Math.round(RADIUS_MILES * MILES_TO_METERS);

      // DOM elements for radius and ZIP
      const radiusMilesInput = document.getElementById("radiusMiles");
      const radiusMetersSpan = document.getElementById("radiusMeters");
      const radiusDisplay = document.getElementById("radiusDisplay");
      const zipCodeInput = document.getElementById("zipCode");
      const zipDisplay = document.getElementById("zipDisplay");
      const coordsDisplay = document.getElementById("coordsDisplay");
      const statusMessage = document.createElement("div");
      statusMessage.className = "status-message";
      zipCodeInput.parentNode.appendChild(statusMessage);

      // Update radius in meters when miles input changes
      radiusMilesInput.addEventListener("input", function() {
        RADIUS_MILES = parseFloat(this.value) || 30;
        window.RADIUS = Math.round(RADIUS_MILES * MILES_TO_METERS);
        radiusMetersSpan.textContent = window.RADIUS;
        radiusDisplay.textContent = RADIUS_MILES;
      });

      // Geocode ZIP code using Census Geocoding API - make it globally accessible
      window.geocodeZipCode = async function geocodeZipCode(zipCode) {
        statusMessage.textContent = `Looking up coordinates for ${zipCode}...`;
        statusMessage.style.color = "#666";

        try {
          // Try using Census Geocoding API with no-cors mode
          try {
            const response = await fetch(`https://geocoding.geo.census.gov/geocoder/locations/address?benchmark=2020&format=json&zip=${zipCode}`, {
              mode: 'no-cors',
              headers: {
                'Accept': 'application/json'
              }
            });

            // Note: With no-cors, we can't actually read the response content
            // So we'll immediately fall back to our local database
            throw new Error("Using no-cors mode, falling back to local database");

          } catch (corsError) {
            console.warn("CORS issue with Census API, using fallback method");
            // Continue to fallback method
          }
          
          // Fallback: Use a hardcoded mapping of common ZIP codes
          const zipCoordinates = {
            // North Suburbs
            "60002": { lat: 42.4639, lng: -87.9957 }, // Antioch, IL
            "60004": { lat: 42.0828, lng: -87.9803 }, // Arlington Heights, IL
            "60005": { lat: 42.0654, lng: -87.9806 }, // Arlington Heights, IL
            "60007": { lat: 42.0372, lng: -87.9925 }, // Elk Grove Village, IL
            "60008": { lat: 42.0503, lng: -88.0198 }, // Rolling Meadows, IL
            "60010": { lat: 42.1543, lng: -88.1362 }, // Barrington, IL
            "60012": { lat: 42.2297, lng: -88.3031 }, // Crystal Lake, IL
            "60013": { lat: 42.2389, lng: -88.3173 }, // Cary, IL
            "60014": { lat: 42.2411, lng: -88.3162 }, // Crystal Lake, IL
            "60015": { lat: 42.1711, lng: -87.7890 }, // Deerfield, IL
            "60016": { lat: 42.0411, lng: -87.8878 }, // Des Plaines, IL
            "60018": { lat: 42.0064, lng: -87.9339 }, // Des Plaines, IL
            "60020": { lat: 42.2364, lng: -88.1962 }, // Fox Lake, IL
            "60021": { lat: 42.1917, lng: -88.2356 }, // Fox River Grove, IL
            "60022": { lat: 42.1464, lng: -87.7859 }, // Glencoe, IL
            "60025": { lat: 42.0697, lng: -87.8270 }, // Glenview, IL
            "60026": { lat: 42.0697, lng: -87.8270 }, // Glenview, IL
            "60029": { lat: 42.0411, lng: -87.8878 }, // Golf, IL
            "60030": { lat: 42.3828, lng: -87.9581 }, // Grayslake, IL
            "60031": { lat: 42.3756, lng: -87.9339 }, // Gurnee, IL
            "60035": { lat: 42.1856, lng: -87.7996 }, // Highland Park, IL
            "60040": { lat: 42.2078, lng: -87.8320 }, // Highwood, IL
            "60041": { lat: 42.4142, lng: -88.0931 }, // Ingleside, IL
            "60042": { lat: 42.4500, lng: -88.0964 }, // Island Lake, IL
            "60043": { lat: 42.1439, lng: -87.7645 }, // Kenilworth, IL
            "60044": { lat: 42.2625, lng: -87.8414 }, // Lake Bluff, IL
            "60045": { lat: 42.2336, lng: -87.8481 }, // Lake Forest, IL
            "60046": { lat: 42.4139, lng: -87.8831 }, // Lake Villa, IL
            "60047": { lat: 42.1950, lng: -88.0797 }, // Lake Zurich, IL
            "60048": { lat: 42.2836, lng: -87.9400 }, // Libertyville, IL
            "60050": { lat: 42.3331, lng: -88.2728 }, // McHenry, IL
            "60051": { lat: 42.3500, lng: -88.2667 }, // McHenry, IL
            "60053": { lat: 42.0664, lng: -87.7479 }, // Morton Grove, IL
            "60056": { lat: 42.0806, lng: -87.9367 }, // Mount Prospect, IL
            "60060": { lat: 42.3142, lng: -87.9464 }, // Mundelein, IL
            "60061": { lat: 42.2178, lng: -87.9125 }, // Vernon Hills, IL
            "60062": { lat: 42.1467, lng: -87.7925 }, // Northbrook, IL
            "60064": { lat: 42.3000, lng: -87.8333 }, // North Chicago, IL
            "60067": { lat: 42.1103, lng: -88.0539 }, // Palatine, IL
            "60068": { lat: 42.0111, lng: -87.8406 }, // Park Ridge, IL
            "60069": { lat: 42.1825, lng: -88.0017 }, // Lincolnshire, IL
            "60070": { lat: 42.1489, lng: -88.0831 }, // Prospect Heights, IL
            "60073": { lat: 42.4500, lng: -88.0000 }, // Round Lake, IL
            "60074": { lat: 42.0483, lng: -88.0817 }, // Palatine, IL
            "60076": { lat: 42.0397, lng: -87.7337 }, // Skokie, IL
            "60077": { lat: 42.0230, lng: -87.7581 }, // Skokie, IL
            "60081": { lat: 42.4167, lng: -88.1333 }, // Spring Grove, IL
            "60082": { lat: 42.1272, lng: -87.8625 }, // Techny, IL
            "60083": { lat: 42.4500, lng: -87.9833 }, // Wadsworth, IL
            "60084": { lat: 42.3333, lng: -88.0000 }, // Wauconda, IL
            "60085": { lat: 42.3639, lng: -87.8447 }, // Waukegan, IL
            "60087": { lat: 42.3639, lng: -87.8447 }, // Waukegan, IL
            "60089": { lat: 42.1492, lng: -87.9264 }, // Buffalo Grove, IL
            "60090": { lat: 42.1417, lng: -87.9278 }, // Wheeling, IL
            "60091": { lat: 42.0780, lng: -87.7106 }, // Wilmette, IL
            "60093": { lat: 42.1083, lng: -87.7340 }, // Winnetka, IL
            "60096": { lat: 42.1967, lng: -87.9472 }, // Wauconda, IL
            "60099": { lat: 42.4464, lng: -87.8031 }, // Zion, IL

            // Chicago City Proper
            "60601": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60602": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60603": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60604": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60605": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60606": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60607": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60608": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Bridgeport/Chinatown)
            "60609": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Bridgeport)
            "60610": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near North Side)
            "60611": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Magnificent Mile/Gold Coast)
            "60612": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near West Side)
            "60613": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Lakeview)
            "60614": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Lincoln Park)
            "60615": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Kenwood/Hyde Park)
            "60616": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near South Side)
            "60617": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Shore)
            "60618": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (North Center/Irving Park)
            "60619": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Chatham)
            "60620": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Greater Grand Crossing)
            "60621": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Fuller Park)
            "60622": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Wicker Park/Bucktown)
            "60623": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Little Village)
            "60624": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (North Lawndale)
            "60625": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Albany Park)
            "60626": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Rogers Park)
            "60628": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Roseland)
            "60629": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Gage Park)
            "60630": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Jefferson Park)
            "60631": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (O'Hare)
            "60632": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (McKinley Park)
            "60633": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Hegewisch)
            "60634": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Belmont Cragin)
            "60636": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (New City)
            "60637": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Woodlawn)
            "60638": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Ashburn)
            "60639": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Hermosa)
            "60640": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Uptown)
            "60641": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Portage Park)
            "60642": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near West Side)
            "60643": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Morgan Park)
            "60644": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Austin)
            "60645": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Ridge)
            "60646": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Norwood Park)
            "60647": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Logan Square)
            "60649": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Shore)
            "60651": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Garfield Park)
            "60652": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Elsdon)
            "60653": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Oakland)
            "60654": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (River North)
            "60655": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Mount Greenwood)
            "60656": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (O'Hare)
            "60657": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Lakeview)
            "60659": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Ridge)
            "60660": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Edgewater)
            "60661": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Loop)
            "60664": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near North Side)
            "60666": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (O'Hare Airport)
            "60668": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near West Side)
            "60669": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near West Side)
            "60670": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60673": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60674": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60675": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60677": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60678": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60680": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60681": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60682": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60684": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60685": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60686": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60687": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60688": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60689": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60690": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60691": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60693": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60694": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60695": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60696": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60697": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60699": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)

            // Additional Chicago South Side and Neighborhoods
            "60827": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Calumet Heights)
            "60628": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Roseland/Pullman)
            "60617": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Shore/Rainbow Beach)
            "60649": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Shore)
            "60619": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Chatham/Avalon Park)
            "60620": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Greater Grand Crossing)
            "60637": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Woodlawn/University of Chicago)
            "60615": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Kenwood/Hyde Park)
            "60653": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Oakland/Douglas)
            "60621": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Fuller Park/Armour Square)
            "60609": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Bridgeport/McKinley Park)
            "60608": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Bridgeport/Chinatown)
            "60616": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near South Side/South Loop)
            "60605": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Loop/Museum Campus)

            // West Suburbs
            "60101": { lat: 41.9372, lng: -88.0942 }, // Addison, IL
            "60102": { lat: 42.1656, lng: -88.2831 }, // Algonquin, IL
            "60103": { lat: 42.0500, lng: -88.2833 }, // Bartlett, IL
            "60106": { lat: 41.9597, lng: -88.0100 }, // Bensenville, IL
            "60107": { lat: 42.0372, lng: -88.2806 }, // Streamwood, IL
            "60108": { lat: 41.9372, lng: -88.1342 }, // Bloomingdale, IL
            "60109": { lat: 42.0667, lng: -88.8167 }, // Burlington, IL
            "60110": { lat: 42.0978, lng: -88.2789 }, // Carpentersville, IL
            "60115": { lat: 41.9294, lng: -88.7500 }, // DeKalb, IL
            "60118": { lat: 42.0950, lng: -88.2833 }, // East Dundee, IL
            "60119": { lat: 41.8900, lng: -88.4700 }, // Elburn, IL
            "60120": { lat: 42.0372, lng: -88.2806 }, // Elgin, IL
            "60123": { lat: 42.0372, lng: -88.2806 }, // Elgin, IL
            "60124": { lat: 42.0939, lng: -88.2892 }, // Elgin, IL
            "60126": { lat: 41.8994, lng: -87.9403 }, // Elmhurst, IL
            "60133": { lat: 41.9950, lng: -88.1342 }, // Hanover Park, IL
            "60134": { lat: 41.8875, lng: -88.3053 }, // Geneva, IL
            "60136": { lat: 42.1000, lng: -88.3333 }, // Gilberts, IL
            "60139": { lat: 41.9114, lng: -88.0789 }, // Glendale Heights, IL
            "60140": { lat: 42.0833, lng: -88.5333 }, // Hampshire, IL
            "60142": { lat: 42.1667, lng: -88.4500 }, // Huntley, IL
            "60143": { lat: 41.9564, lng: -88.0853 }, // Itasca, IL
            "60152": { lat: 42.2333, lng: -88.4000 }, // Marengo, IL
            "60153": { lat: 41.8764, lng: -87.8631 }, // Maywood, IL
            "60154": { lat: 41.8828, lng: -87.9589 }, // Westchester, IL
            "60156": { lat: 42.2500, lng: -88.3833 }, // Lake in the Hills, IL
            "60157": { lat: 42.0372, lng: -88.1342 }, // Medinah, IL
            "60172": { lat: 42.0089, lng: -88.0797 }, // Roselle, IL
            "60173": { lat: 42.0456, lng: -88.1152 }, // Schaumburg, IL
            "60174": { lat: 41.9142, lng: -88.3087 }, // St. Charles, IL
            "60175": { lat: 41.9142, lng: -88.3087 }, // St. Charles, IL
            "60177": { lat: 42.0333, lng: -88.3333 }, // South Elgin, IL
            "60178": { lat: 42.0500, lng: -88.7167 }, // Sycamore, IL
            "60181": { lat: 41.8700, lng: -87.9650 }, // Villa Park, IL
            "60184": { lat: 41.8517, lng: -88.2075 }, // Wayne, IL
            "60185": { lat: 41.8528, lng: -88.2042 }, // West Chicago, IL
            "60187": { lat: 41.8656, lng: -88.1069 }, // Wheaton, IL
            "60188": { lat: 41.9322, lng: -88.1536 }, // Carol Stream, IL
            "60189": { lat: 41.7950, lng: -88.0700 }, // Wheaton, IL
            "60190": { lat: 41.8244, lng: -88.1561 }, // Winfield, IL
            "60191": { lat: 41.9578, lng: -87.9981 }, // Wood Dale, IL
            "60192": { lat: 42.0303, lng: -88.2073 }, // Hoffman Estates, IL
            "60193": { lat: 42.0303, lng: -88.2073 }, // Schaumburg, IL
            "60194": { lat: 42.0456, lng: -88.1152 }, // Schaumburg, IL
            "60195": { lat: 42.0456, lng: -88.1152 }, // Schaumburg, IL
            
            // Evanston/Skokie
            "60201": { lat: 42.0464, lng: -87.6931 }, // Evanston, IL
            "60202": { lat: 42.0464, lng: -87.6931 }, // Evanston, IL
            "60203": { lat: 42.0464, lng: -87.6931 }, // Evanston, IL
            
            // Oak Park/River Forest
            "60301": { lat: 41.8856, lng: -87.7845 }, // Oak Park, IL
            "60302": { lat: 41.8856, lng: -87.7845 }, // Oak Park, IL
            "60304": { lat: 41.8856, lng: -87.7845 }, // Oak Park, IL
            "60305": { lat: 41.8953, lng: -87.8339 }, // River Forest, IL
            "60521": { lat: 41.7992, lng: -87.9403 }, // Hinsdale, IL
            "60523": { lat: 41.8200, lng: -87.9650 }, // Oak Brook, IL
            "60525": { lat: 41.8056, lng: -87.8631 }, // La Grange, IL
            "60526": { lat: 41.8200, lng: -87.8900 }, // La Grange Park, IL
            "60532": { lat: 41.8089, lng: -88.0756 }, // Lisle, IL
            "60540": { lat: 41.7508, lng: -88.1535 }, // Naperville, IL
            "60563": { lat: 41.7508, lng: -88.1535 }, // Naperville, IL
            "60564": { lat: 41.7508, lng: -88.1535 }, // Naperville, IL
            "60565": { lat: 41.7508, lng: -88.1535 },  // Naperville, IL
            
            // South Bay Los Angeles
            "90245": { lat: 33.9164, lng: -118.4016 }, // El Segundo, CA
            "90254": { lat: 33.8598, lng: -118.3965 }, // Hermosa Beach, CA
            "90266": { lat: 33.8847, lng: -118.4109 }, // Manhattan Beach, CA
            "90277": { lat: 33.8236, lng: -118.3887 }, // Redondo Beach, CA
            "90278": { lat: 33.8729, lng: -118.3765 }, // Redondo Beach, CA
            "90501": { lat: 33.8358, lng: -118.3406 }, // Torrance, CA
            "90503": { lat: 33.8358, lng: -118.3406 }, // Torrance, CA
            "90505": { lat: 33.8058, lng: -118.3312 }, // Torrance, CA
            
            // Palos Verdes Peninsula
            "90274": { lat: 33.7866, lng: -118.3896 }, // Palos Verdes Estates, CA
            "90275": { lat: 33.7444, lng: -118.3870 }, // Rancho Palos Verdes, CA
            
            // San Pedro
            "90731": { lat: 33.7361, lng: -118.2922 }, // San Pedro, CA
            "90732": { lat: 33.7483, lng: -118.3120 }, // San Pedro, CA
            
            // Long Beach
            "90802": { lat: 33.7701, lng: -118.1937 }, // Long Beach, CA
            "90803": { lat: 33.7605, lng: -118.1306 }, // Long Beach, CA
            "90804": { lat: 33.7900, lng: -118.1581 }, // Long Beach, CA
            "90805": { lat: 33.8633, lng: -118.1801 }, // Long Beach, CA
            "90806": { lat: 33.8019, lng: -118.1873 }, // Long Beach, CA
            "90807": { lat: 33.8303, lng: -118.1801 }, // Long Beach, CA
            "90808": { lat: 33.8303, lng: -118.1143 }, // Long Beach, CA
            "90810": { lat: 33.8108, lng: -118.2250 }, // Long Beach, CA
            "90813": { lat: 33.7866, lng: -118.1873 }, // Long Beach, CA
            "90814": { lat: 33.7701, lng: -118.1471 }, // Long Beach, CA
            "90815": { lat: 33.7941, lng: -118.1306 }, // Long Beach, CA
            
            // Venice Beach
            "90291": { lat: 33.9850, lng: -118.4695 }, // Venice, CA
            
            // Culver City
            "90230": { lat: 34.0211, lng: -118.3965 }, // Culver City, CA
            "90232": { lat: 34.0211, lng: -118.3965 }, // Culver City, CA
            
            // Hawthorne
            "90250": { lat: 33.9164, lng: -118.3525 }, // Hawthorne, CA
            
            // Ames, IA
            "50010": { lat: 42.0308, lng: -93.6319 }, // Ames, IA
            "50011": { lat: 42.0308, lng: -93.6319 }, // Ames, IA (Iowa State University)
            "50014": { lat: 42.0308, lng: -93.6319 }, // Ames, IA
            
            // New York City
            "10001": { lat: 40.7503, lng: -73.9972 }, // Manhattan, NY
            "10002": { lat: 40.7168, lng: -73.9861 }, // Manhattan, NY
            "10003": { lat: 40.7318, lng: -73.9888 }, // Manhattan, NY
            "10007": { lat: 40.7133, lng: -74.0070 }, // Manhattan, NY
            "10011": { lat: 40.7399, lng: -74.0021 }, // Manhattan, NY
            "10016": { lat: 40.7459, lng: -73.9776 }, // Manhattan, NY
            "10017": { lat: 40.7520, lng: -73.9736 }, // Manhattan, NY
            "10018": { lat: 40.7551, lng: -73.9932 }, // Manhattan, NY
            "10019": { lat: 40.7662, lng: -73.9862 }, // Manhattan, NY
            "10021": { lat: 40.7690, lng: -73.9550 }, // Manhattan, NY
            "10022": { lat: 40.7583, lng: -73.9685 }, // Manhattan, NY
            "10023": { lat: 40.7769, lng: -73.9822 }, // Manhattan, NY
            "10024": { lat: 40.7892, lng: -73.9745 }, // Manhattan, NY
            "10025": { lat: 40.7989, lng: -73.9669 }, // Manhattan, NY
            "10028": { lat: 40.7764, lng: -73.9546 }, // Manhattan, NY
            "10036": { lat: 40.7602, lng: -73.9896 }, // Manhattan, NY
            "10128": { lat: 40.7808, lng: -73.9497 }, // Manhattan, NY
            "11201": { lat: 40.6958, lng: -73.9897 }, // Brooklyn, NY
            "11215": { lat: 40.6710, lng: -73.9860 }, // Brooklyn, NY
            "11217": { lat: 40.6829, lng: -73.9790 }, // Brooklyn, NY
            "11220": { lat: 40.6340, lng: -74.0110 }, // Brooklyn, NY
            "11222": { lat: 40.7277, lng: -73.9443 }, // Brooklyn, NY
            "11231": { lat: 40.6795, lng: -74.0029 }, // Brooklyn, NY
            "11238": { lat: 40.6795, lng: -73.9643 }, // Brooklyn, NY
            "11211": { lat: 40.7121, lng: -73.9538 }, // Brooklyn, NY (Williamsburg)
            "11249": { lat: 40.7121, lng: -73.9538 }, // Brooklyn, NY (Williamsburg)
            "11101": { lat: 40.7464, lng: -73.9394 }, // Queens, NY (Long Island City)
            "11106": { lat: 40.7628, lng: -73.9311 }, // Queens, NY (Astoria)
            "11103": { lat: 40.7628, lng: -73.9125 }, // Queens, NY (Astoria)
            "10451": { lat: 40.8200, lng: -73.9261 }, // Bronx, NY
            "10452": { lat: 40.8367, lng: -73.9172 }, // Bronx, NY
            "10453": { lat: 40.8521, lng: -73.9119 }, // Bronx, NY
            "10301": { lat: 40.6368, lng: -74.1181 }, // Staten Island, NY
            "10304": { lat: 40.6097, lng: -74.0859 }, // Staten Island, NY
            "10305": { lat: 40.5972, lng: -74.0759 }, // Staten Island, NY
            
            // Omaha, NE
            "68102": { lat: 41.2627, lng: -95.9350 }, // Omaha, NE (Downtown)
            "68104": { lat: 41.2917, lng: -96.0066 }, // Omaha, NE
            "68105": { lat: 41.2431, lng: -95.9577 }, // Omaha, NE
            "68106": { lat: 41.2431, lng: -96.0066 }, // Omaha, NE
            "68107": { lat: 41.2139, lng: -95.9577 }, // Omaha, NE
            "68108": { lat: 41.2335, lng: -95.9350 }, // Omaha, NE
            "68110": { lat: 41.2917, lng: -95.9350 }, // Omaha, NE
            "68111": { lat: 41.2917, lng: -95.9577 }, // Omaha, NE
            "68112": { lat: 41.3403, lng: -95.9577 }, // Omaha, NE
            "68114": { lat: 41.2627, lng: -96.0554 }, // Omaha, NE
            "68116": { lat: 41.2917, lng: -96.1531 }, // Omaha, NE
            "68117": { lat: 41.2139, lng: -96.0066 }, // Omaha, NE
            "68118": { lat: 41.2627, lng: -96.1531 }, // Omaha, NE
            "68130": { lat: 41.2335, lng: -96.1531 }, // Omaha, NE
            "68131": { lat: 41.2723, lng: -95.9577 }, // Omaha, NE
            "68132": { lat: 41.2723, lng: -96.0066 }, // Omaha, NE
            "68134": { lat: 41.2917, lng: -96.0554 }, // Omaha, NE
            "68144": { lat: 41.2139, lng: -96.1043 }, // Omaha, NE
            "68154": { lat: 41.2335, lng: -96.1043 }, // Omaha, NE
            
            // Wilmington, DE
            "19801": { lat: 39.7459, lng: -75.5466 }, // Wilmington, DE (Downtown)
            "19802": { lat: 39.7598, lng: -75.5276 }, // Wilmington, DE
            "19803": { lat: 39.7929, lng: -75.5466 }, // Wilmington, DE
            "19804": { lat: 39.7321, lng: -75.5847 }, // Wilmington, DE
            "19805": { lat: 39.7321, lng: -75.5656 }, // Wilmington, DE
            "19806": { lat: 39.7598, lng: -75.5656 }, // Wilmington, DE
            "19807": { lat: 39.7790, lng: -75.5847 }, // Wilmington, DE
            "19808": { lat: 39.7321, lng: -75.6228 }, // Wilmington, DE
            "19809": { lat: 39.7598, lng: -75.4895 }, // Wilmington, DE
            "19810": { lat: 39.8260, lng: -75.5466 }, // Wilmington, DE
            
            // Winston-Salem, NC
            "27101": { lat: 36.0999, lng: -80.2442 }, // Winston-Salem, NC (Downtown)
            "27103": { lat: 36.0638, lng: -80.3218 }, // Winston-Salem, NC
            "27104": { lat: 36.0999, lng: -80.3218 }, // Winston-Salem, NC
            "27105": { lat: 36.1360, lng: -80.2442 }, // Winston-Salem, NC
            "27106": { lat: 36.1360, lng: -80.3218 }, // Winston-Salem, NC
            "27107": { lat: 36.0277, lng: -80.2442 }, // Winston-Salem, NC
            "27127": { lat: 36.0277, lng: -80.3218 }, // Winston-Salem, NC
            
            // San Fernando Valley & Thousand Oaks
            "91301": { lat: 34.1517, lng: -118.7798 }, // Agoura Hills, CA
            "91302": { lat: 34.1478, lng: -118.7126 }, // Calabasas, CA
            "91303": { lat: 34.2011, lng: -118.6031 }, // Canoga Park, CA
            "91304": { lat: 34.2011, lng: -118.6031 }, // Canoga Park, CA
            "91306": { lat: 34.2011, lng: -118.5257 }, // Winnetka, CA
            "91307": { lat: 34.1883, lng: -118.6417 }, // West Hills, CA
            "91311": { lat: 34.2501, lng: -118.6417 }, // Chatsworth, CA
            "91316": { lat: 34.1517, lng: -118.5031 }, // Encino, CA
            "91324": { lat: 34.2351, lng: -118.5386 }, // Northridge, CA
            "91325": { lat: 34.2351, lng: -118.5386 }, // Northridge, CA
            "91330": { lat: 34.2410, lng: -118.5300 }, // Northridge, CA (CSUN)
            "91335": { lat: 34.2011, lng: -118.5386 }, // Reseda, CA
            "91340": { lat: 34.2881, lng: -118.4386 }, // San Fernando, CA
            "91342": { lat: 34.3231, lng: -118.4257 }, // Sylmar, CA
            "91343": { lat: 34.2351, lng: -118.4644 }, // North Hills, CA
            "91344": { lat: 34.2881, lng: -118.5031 }, // Granada Hills, CA
            "91345": { lat: 34.2881, lng: -118.4644 }, // Mission Hills, CA
            "91350": { lat: 34.4531, lng: -118.5257 }, // Santa Clarita, CA
            "91351": { lat: 34.4531, lng: -118.5257 }, // Santa Clarita, CA
            "91352": { lat: 34.2351, lng: -118.3870 }, // Sun Valley, CA
            "91354": { lat: 34.4531, lng: -118.5257 }, // Santa Clarita, CA
            "91355": { lat: 34.4531, lng: -118.5644 }, // Santa Clarita, CA
            "91356": { lat: 34.1517, lng: -118.5386 }, // Tarzana, CA
            "91360": { lat: 34.1700, lng: -118.8500 }, // Thousand Oaks, CA
            "91361": { lat: 34.1478, lng: -118.8185 }, // Westlake Village, CA
            "91362": { lat: 34.1700, lng: -118.8500 }, // Thousand Oaks, CA
            "91364": { lat: 34.1517, lng: -118.5644 }, // Woodland Hills, CA
            "91367": { lat: 34.1517, lng: -118.6031 }, // Woodland Hills, CA
            "91401": { lat: 34.1794, lng: -118.4386 }, // Van Nuys, CA
            "91402": { lat: 34.2351, lng: -118.4386 }, // Panorama City, CA
            "91403": { lat: 34.1517, lng: -118.4644 }, // Sherman Oaks, CA
            "91405": { lat: 34.1794, lng: -118.4386 }, // Van Nuys, CA
            "91406": { lat: 34.1794, lng: -118.5031 }, // Van Nuys, CA
            "91411": { lat: 34.1794, lng: -118.4644 }, // Van Nuys, CA
            "91423": { lat: 34.1517, lng: -118.4386 }, // Sherman Oaks, CA
            "91436": { lat: 34.1517, lng: -118.4902 }, // Encino, CA
            
            // Las Vegas
            "89101": { lat: 36.1750, lng: -115.1372 }, // Las Vegas, NV
            "89102": { lat: 36.1750, lng: -115.1933 }, // Las Vegas, NV
            "89103": { lat: 36.1147, lng: -115.1933 }, // Las Vegas, NV
            "89104": { lat: 36.1750, lng: -115.0811 }, // Las Vegas, NV
            "89106": { lat: 36.2075, lng: -115.1653 }, // Las Vegas, NV
            "89107": { lat: 36.1750, lng: -115.2214 }, // Las Vegas, NV
            "89108": { lat: 36.2075, lng: -115.2214 }, // Las Vegas, NV
            "89109": { lat: 36.1147, lng: -115.1653 }, // Las Vegas, NV (The Strip)
            "89110": { lat: 36.1750, lng: -115.0531 }, // Las Vegas, NV
            "89113": { lat: 36.0869, lng: -115.2495 }, // Las Vegas, NV
            "89117": { lat: 36.1425, lng: -115.2775 }, // Las Vegas, NV

            // San Diego County - Poway
            "92064": { lat: 32.9628, lng: -117.0359 }, // Poway, CA

            // Orange County - San Juan Capistrano
            "92675": { lat: 33.5017, lng: -117.6628 }, // San Juan Capistrano, CA

            // Silicon Valley Area
            "94301": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "94302": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "94303": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "94304": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "94305": { lat: 37.4419, lng: -122.1430 }, // Stanford, CA
            "94306": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "95014": { lat: 37.3230, lng: -122.0322 }, // Cupertino, CA
            "95051": { lat: 37.3688, lng: -121.9678 }, // Santa Clara, CA
            "95054": { lat: 37.3688, lng: -121.9678 }, // Santa Clara, CA
            "95070": { lat: 37.3541, lng: -122.0522 }, // Saratoga, CA
            "95110": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95111": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95112": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95113": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95116": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95117": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95118": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95119": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95120": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95121": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95122": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95123": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95124": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95125": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95126": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95127": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95128": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95129": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95130": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95131": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95132": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95133": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95134": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95135": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95136": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95138": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95139": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "94085": { lat: 37.4224, lng: -122.0856 }, // Sunnyvale, CA
            "94086": { lat: 37.4224, lng: -122.0856 }, // Sunnyvale, CA
            "94087": { lat: 37.4224, lng: -122.0856 }, // Sunnyvale, CA
            "94089": { lat: 37.4224, lng: -122.0856 }, // Sunnyvale, CA
            "94041": { lat: 37.3861, lng: -122.0839 }, // Mountain View, CA
            "94043": { lat: 37.3861, lng: -122.0839 }, // Mountain View, CA
            "94024": { lat: 37.4852, lng: -122.1483 }, // Los Altos, CA
            "94022": { lat: 37.4852, lng: -122.1483 }, // Los Altos, CA
            "94040": { lat: 37.3861, lng: -122.0839 }, // Mountain View, CA
            "94035": { lat: 37.4419, lng: -122.1430 }, // Milpitas, CA
            "95035": { lat: 37.4323, lng: -121.9018 }, // Milpitas, CA

            // Ventura County
            "93001": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA
            "93003": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA
            "93004": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA
            "93006": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA
            "93009": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA

            // Seattle Metropolitan Area
            // Seattle
            "98101": { lat: 47.6062, lng: -122.3321 }, // Seattle, WA (Downtown)
            "98102": { lat: 47.6205, lng: -122.3212 }, // Seattle, WA (Capitol Hill)
            "98103": { lat: 47.6740, lng: -122.3419 }, // Seattle, WA (Fremont/Wallingford)
            "98104": { lat: 47.6021, lng: -122.3321 }, // Seattle, WA (Pioneer Square)
            "98105": { lat: 47.6606, lng: -122.3031 }, // Seattle, WA (University District)
            "98106": { lat: 47.5287, lng: -122.3487 }, // Seattle, WA (White Center)
            "98107": { lat: 47.6684, lng: -122.3762 }, // Seattle, WA (Ballard)
            "98108": { lat: 47.5287, lng: -122.3212 }, // Seattle, WA (South Park)
            "98109": { lat: 47.6205, lng: -122.3487 }, // Seattle, WA (South Lake Union)
            "98112": { lat: 47.6205, lng: -122.3031 }, // Seattle, WA (Capitol Hill East)
            "98115": { lat: 47.6897, lng: -122.3031 }, // Seattle, WA (Northgate)
            "98116": { lat: 47.5649, lng: -122.3762 }, // Seattle, WA (West Seattle)
            "98117": { lat: 47.6897, lng: -122.3762 }, // Seattle, WA (Ballard North)
            "98118": { lat: 47.5287, lng: -122.2762 }, // Seattle, WA (Rainier Valley)
            "98119": { lat: 47.6354, lng: -122.3762 }, // Seattle, WA (Interbay)
            "98121": { lat: 47.6149, lng: -122.3487 }, // Seattle, WA (Belltown)
            "98122": { lat: 47.6062, lng: -122.3031 }, // Seattle, WA (Central District)
            "98125": { lat: 47.7231, lng: -122.3031 }, // Seattle, WA (Northgate North)
            "98126": { lat: 47.5649, lng: -122.3487 }, // Seattle, WA (West Seattle South)
            "98133": { lat: 47.7231, lng: -122.3487 }, // Seattle, WA (North Seattle)
            "98134": { lat: 47.5649, lng: -122.3212 }, // Seattle, WA (SODO)
            "98136": { lat: 47.5287, lng: -122.3762 }, // Seattle, WA (Burien/Highline)
            "98144": { lat: 47.5649, lng: -122.2762 }, // Seattle, WA (Mount Baker)
            "98146": { lat: 47.5287, lng: -122.3487 }, // Seattle, WA (Burien)
            "98148": { lat: 47.4287, lng: -122.3212 }, // Seattle, WA (SeaTac)
            "98154": { lat: 47.5649, lng: -122.3321 }, // Seattle, WA (South Seattle)
            "98155": { lat: 47.7231, lng: -122.2762 }, // Seattle, WA (Shoreline)
            "98158": { lat: 47.4649, lng: -122.2762 }, // Seattle, WA (Tukwila)
            "98164": { lat: 47.5649, lng: -122.3321 }, // Seattle, WA (Industrial District)
            "98166": { lat: 47.4649, lng: -122.3487 }, // Seattle, WA (Normandy Park)
            "98168": { lat: 47.4649, lng: -122.3212 }, // Seattle, WA (Tukwila South)
            "98177": { lat: 47.7564, lng: -122.3762 }, // Seattle, WA (Shoreline West)
            "98178": { lat: 47.4649, lng: -122.2762 }, // Seattle, WA (Tukwila East)
            "98188": { lat: 47.4287, lng: -122.2762 }, // Seattle, WA (SeaTac East)
            "98199": { lat: 47.6354, lng: -122.4031 }, // Seattle, WA (Magnolia)

            // Redmond
            "98052": { lat: 47.6740, lng: -122.1215 }, // Redmond, WA
            "98053": { lat: 47.6740, lng: -122.1215 }, // Redmond, WA (Microsoft Campus area)

            // Bellevue
            "98004": { lat: 47.6101, lng: -122.2015 }, // Bellevue, WA (Downtown)
            "98005": { lat: 47.6101, lng: -122.1515 }, // Bellevue, WA (East)
            "98006": { lat: 47.5649, lng: -122.1515 }, // Bellevue, WA (South)
            "98007": { lat: 47.6101, lng: -122.1215 }, // Bellevue, WA (Crossroads)
            "98008": { lat: 47.6354, lng: -122.1515 }, // Bellevue, WA (North)

            // Medina
            "98039": { lat: 47.6240, lng: -122.2304 }, // Medina, WA

            // Florida - Fort Myers Area (Lee County)
            "33901": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL (Downtown)
            "33902": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33903": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33904": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33905": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33906": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33907": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33908": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33909": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33912": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33913": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33914": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33915": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33916": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33917": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33918": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33919": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33920": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33921": { lat: 26.5628, lng: -81.9495 }, // Estero, FL
            "33922": { lat: 26.4614, lng: -81.8081 }, // Fort Myers Beach, FL
            "33924": { lat: 26.4614, lng: -81.8081 }, // Fort Myers Beach, FL
            "33928": { lat: 26.5312, lng: -82.0251 }, // Bonita Springs, FL
            "33931": { lat: 26.3587, lng: -81.8723 }, // Lehigh Acres, FL
            "33936": { lat: 26.3587, lng: -81.8723 }, // Lehigh Acres, FL
            "33957": { lat: 26.7153, lng: -81.7787 }, // North Fort Myers, FL
            "33966": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33967": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33971": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33972": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33973": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33974": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33976": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33990": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33991": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33993": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL

            // Florida - Sarasota Area (Sarasota County)
            "34201": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34202": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34203": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34205": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34207": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34208": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34209": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34210": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34211": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34212": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34215": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34216": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34217": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34219": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34221": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34222": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34223": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34224": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34228": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34229": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34230": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34231": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34232": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34233": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34234": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34235": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34236": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34237": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34238": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34239": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34240": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34241": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34242": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34243": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34251": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34260": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34264": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34265": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34266": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34267": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34268": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34269": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34270": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34274": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34275": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34276": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34277": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34278": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34280": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34281": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34282": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34284": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34285": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34286": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34287": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34288": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34289": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34290": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34291": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34292": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34293": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34295": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL

            // Florida - Clearwater/Tampa Bay Area (Pinellas County)
            "33755": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33756": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33759": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33760": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33761": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33762": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33763": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33764": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33765": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33767": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33770": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33771": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33772": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33773": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33774": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33775": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33776": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33777": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33778": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33779": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33780": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33781": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33782": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33784": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33785": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33786": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33787": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL

            // San Francisco, CA
            "94102": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Downtown/Civic Center)
            "94103": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (SOMA)
            "94104": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Financial District)
            "94105": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (SOMA/Rincon Hill)
            "94107": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Potrero Hill)
            "94108": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Chinatown/Nob Hill)
            "94109": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Polk Gulch/Russian Hill)
            "94110": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Mission District)
            "94111": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Financial District)
            "94112": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Outer Mission)
            "94114": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Castro/Noe Valley)
            "94115": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Pacific Heights)
            "94116": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Sunset District)
            "94117": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Haight-Ashbury)
            "94118": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Richmond District)
            "94121": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Richmond District)
            "94122": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Sunset District)
            "94123": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Marina District)
            "94124": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Bayview)
            "94127": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (West Portal)
            "94129": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Presidio)
            "94130": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Treasure Island)
            "94131": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Glen Park)
            "94132": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Lake Merced)
            "94133": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (North Beach)
            "94134": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Visitacion Valley)
            "94158": { lat: 37.7749, lng: -122.4194 } // San Francisco, CA (Mission Bay)
          };
          
          // Check if the ZIP code exists in the hardcoded mapping
          if (zipCoordinates[zipCode]) {
            const { lat, lng } = zipCoordinates[zipCode];
            window.LAT = lat;
            window.LNG = lng;
            window.ZIP_CODE = zipCode;
            zipDisplay.textContent = zipCode;
            coordsDisplay.textContent = `${lat}, ${lng}`;
            statusMessage.textContent = `Coordinates found: ${window.LAT}, ${window.LNG}`;
            statusMessage.style.color = "#333";
            return { lat, lng };
          } else {
            throw new Error("ZIP code not found in local database");
          }
        } catch (error) {
          console.error("Error geocoding ZIP code:", error);
          statusMessage.textContent = `Error looking up coordinates: ${error.message}. Using default ZIP ${window.ZIP_CODE}.`;
          statusMessage.style.color = "#d32f2f";
          // Reset input to current valid ZIP
          zipCodeInput.value = window.ZIP_CODE;
          return null;
        }
      }

      // Add event listener to ZIP code input for real-time geocoding
      zipCodeInput.addEventListener("blur", async function() {
        const newZip = this.value.trim();
        if (newZip && /^\d{5}$/.test(newZip) && newZip !== window.ZIP_CODE) {
          await window.geocodeZipCode(newZip);
        }
      });

      // Also handle Enter key on ZIP input
      zipCodeInput.addEventListener("keydown", async function(e) {
        if (e.key === "Enter") {
          const newZip = this.value.trim();
          if (newZip && /^\d{5}$/.test(newZip) && newZip !== window.ZIP_CODE) {
            await window.geocodeZipCode(newZip);
          }
        }
      });
    })();
  </script>


  <script>
    (function () {
      // ======================================================
      // User Categories Storage Helpers
      // ======================================================
      function loadUserCategories() {
        try {
          const stored = localStorage.getItem("userCategories");
          if (!stored) return {};
          return JSON.parse(stored) || {};
        } catch (err) {
          console.error("Error loading user categories:", err);
          return {};
        }
      }
      
      function saveUserCategories(userCategories) {
        try {
          localStorage.setItem("userCategories", JSON.stringify(userCategories));
          return true;
        } catch (err) {
          console.error("Error saving user categories:", err);
          return false;
        }
      }

      // ======================================================
      // User Groups Storage Helpers
      // ======================================================
      function loadUserGroups() {
        try {
          const stored = localStorage.getItem("userGroups");
          if (!stored) return [];
          return JSON.parse(stored) || [];
        } catch (err) {
          console.error("Error loading user groups:", err);
          return [];
        }
      }

      function saveUserGroups(userGroups) {
        try {
          localStorage.setItem("userGroups", JSON.stringify(userGroups));
          return true;
        } catch (err) {
          console.error("Error saving user groups:", err);
          return false;
        }
      }

      // ======================================================
      // Hidden Groups Storage Helpers
      // ======================================================
      function loadHiddenGroups() {
        try {
          const stored = localStorage.getItem("hiddenGroups");
          if (!stored) return [];
          return JSON.parse(stored) || [];
        } catch (err) {
          console.error("Error loading hidden groups:", err);
          return [];
        }
      }

      function saveHiddenGroups(hiddenGroups) {
        try {
          localStorage.setItem("hiddenGroups", JSON.stringify(hiddenGroups));
          return true;
        } catch (err) {
          console.error("Error saving hidden groups:", err);
          return false;
        }
      }

      // ======================================================
      // Delete Group Functionality
      // ======================================================
      function deleteGroup(groupKey) {
        try {
          // 1. Remove all user-defined categories in this group
          const userCategories = loadUserCategories();
          const categoriesToDelete = [];

          Object.entries(userCategories).forEach(([filename, filters]) => {
            if (filters[0] && filters[0].key === groupKey) {
              categoriesToDelete.push(filename);
            }
          });

          // Delete the user categories
          categoriesToDelete.forEach(filename => {
            delete userCategories[filename];
          });
          saveUserCategories(userCategories);

          // 2. Remove the group from user groups if it's a custom group
          const userGroups = loadUserGroups();
          const groupIndex = userGroups.indexOf(groupKey);
          if (groupIndex > -1) {
            userGroups.splice(groupIndex, 1);
            saveUserGroups(userGroups);
          }

          // 3. For built-in groups, we'll hide them by adding to a "hidden groups" list
          const hiddenGroups = loadHiddenGroups();
          if (!hiddenGroups.includes(groupKey)) {
            hiddenGroups.push(groupKey);
            saveHiddenGroups(hiddenGroups);
          }

          // 4. Refresh the UI
          initializeCategoryCheckboxes();

          // 5. Show success message
          console.log(`Group "${groupKey}" deleted successfully`);

        } catch (error) {
          console.error("Error deleting group:", error);
          alert("An error occurred while deleting the group. Please try again.");
        }
      }

      // ======================================================
      // Additional Categories
      // ======================================================
      const CATEGORIES = {
        "liquor_distributors.csv":     [{ key: "shop",     value: "alcohol" }],
        "manufacturers.csv":           [{ key: "industrial", value: "manufacturer" }],
        "hair_salons.csv":             [{ key: "shop",     value: "hairdresser" }],
        "nail_shops.csv":              [{ key: "shop",     value: "beauty" }],
        "auto_body_shops.csv":         [{ key: "shop",     value: "car_repair" }], // same as auto_mechanic
        "montessori_schools.csv":      [{ key: "amenity",  value: "school" }, { key: "school:level", value: "pre_school" }],
        "wealth_management.csv":       [{ key: "office",   value: "financial" }],
        "venture_capital.csv":         [{ key: "office",   value: "financial" }],
        "stock_brokers.csv":           [{ key: "office",   value: "financial" }],
        "medical_practices.csv":       [{ key: "amenity",  value: "clinic" }],
        "hospitals.csv":               [{ key: "amenity",  value: "hospital" }],
        "dental_practices.csv":        [{ key: "amenity",  value: "dentist" }],
        "veterinary_clinics.csv":      [{ key: "amenity",  value: "veterinary" }],
        "nursing_homes.csv":           [{ key: "amenity",  value: "nursing_home" }],
        "assisted_living.csv":         [{ key: "amenity",  value: "social_facility" }],
        "pharmaceutical_companies.csv": [{ key: "shop",    value: "pharmacy" }],
        "manufacturing_industrial.csv": [{ key: "industrial", value: "manufacturer" }],
        "auto_dealerships.csv":        [{ key: "shop",     value: "car" }],
        "banks_financial.csv":         [{ key: "amenity",  value: "bank" }],
        "accounting_firms.csv":        [{ key: "office",   value: "accountant" }],
        "insurance_agencies.csv":      [{ key: "office",   value: "insurance" }],
        "real_estate_agencies.csv":    [{ key: "office",   value: "real_estate" }],
        "educational_institutions.csv": [{ key: "amenity", value: "school" }],
        "daycare_centers.csv":         [{ key: "amenity",  value: "childcare" }],
        "nonprofits.csv":              [{ key: "office",   value: "non_profit_organization" }],
        "government_agencies.csv":     [{ key: "office",   value: "government" }],
        "retail_stores.csv":           [{ key: "shop" }], // generic shop filter
        "restaurants.csv":             [{ key: "amenity",  value: "restaurant" }],
        "hotels_motels.csv":           [{ key: "tourism",  value: "hotel" }],
        "logistics_transport.csv":     [{ key: "office",   value: "logistics" }],
        "engineering_firms.csv":       [{ key: "office",   value: "engineering" }],
        "architecture_design.csv":     [{ key: "office",   value: "architect" }],
        "consulting_firms.csv":        [{ key: "office",   value: "consulting" }],
        "marketing_agencies.csv":      [{ key: "office",   value: "marketing" }],
        "media_production.csv":        [{ key: "office",   value: "media" }],
        "telecom_voip.csv":            [{ key: "office",   value: "telecom" }],
        "energy_utility.csv":          [{ key: "office",   value: "utility" }],
        "gyms_fitness.csv":            [{ key: "leisure",  value: "fitness_centre" }],
        "salons_spas.csv":             [{ key: "shop",     value: "beauty" }],
        "agriculture_farming.csv":     [{ key: "landuse",  value: "farmland" }],
        "aerospace_aviation.csv":      [{ key: "aeroway" }],
        "coworking_spaces.csv":        [{ key: "office",   value: "coworking" }],
      };
    const ADDITIONAL_CATEGORIES = {
        "breweries.csv":          [{ key: "craft",   value: "brewery" }],
        "wineries.csv":           [{ key: "craft",   value: "winery" }],
        "distilleries.csv":       [{ key: "craft",   value: "distillery" }],
        "cafes.csv":              [{ key: "amenity", value: "cafe" }],
        "bars.csv":               [{ key: "amenity", value: "bar" }],
        "pubs.csv":               [{ key: "amenity", value: "pub" }],
        "nightclubs.csv":         [{ key: "amenity", value: "nightclub" }],
        "cinemas.csv":            [{ key: "amenity", value: "cinema" }],
        "art_galleries.csv":      [{ key: "tourism", value: "gallery" }],
        "museums.csv":            [{ key: "tourism", value: "museum" }],
        "bookstores.csv":         [{ key: "shop",    value: "books" }],
        "hardware_stores.csv":    [{ key: "shop",    value: "hardware" }],
        "electronics_stores.csv": [{ key: "shop",    value: "electronics" }],
        "furniture_stores.csv":   [{ key: "shop",    value: "furniture" }],
        "pet_stores.csv":         [{ key: "shop",    value: "pet" }],
        "bakeries.csv":           [{ key: "shop",    value: "bakery" }],
        "convenience_stores.csv": [{ key: "shop",    value: "convenience" }],
        "grocery_stores.csv":     [{ key: "shop",    value: "supermarket" }],
        "dry_cleaners.csv":       [{ key: "shop",    value: "laundry" }],
        "printing_shops.csv":     [{ key: "shop",    value: "printing" }],
        "tattoo_parlors.csv":     [{ key: "shop",    value: "tattoo" }],
        "car_rental.csv":         [{ key: "amenity", value: "car_rental" }],
        "fuel_stations.csv":      [{ key: "amenity", value: "fuel" }]
    };
      const CSV_HEADER = [
        "Name",
        "Street Number",
        "Street Name",
        "City",
        "State",
        "ZIP+4",
        "Phone Number",
        "Fax Number",
        "Email Address",
        "Website",
        "Contact Name(s)"
      ];

      // ======================================================
      // DOM Elements
      // ======================================================
      const runBtn = document.getElementById("runBtn");
      const logDiv = document.getElementById("log");
      const downloadsDiv = document.getElementById("downloads");
      const selectAllCategoriesCheckbox = document.getElementById("selectAllCategories");
      const allCategoriesContainer = document.getElementById("allCategories");
      const downloadAllBtn = document.getElementById("downloadAllFilesBtn");

      // ZIP code and radius elements (needed for runExport)
      const zipCodeInput = document.getElementById("zipCode");
      const zipDisplay = document.getElementById("zipDisplay");

      // Header new entry container for adding groups
      const headerNewEntryContainer = document.getElementById("headerNewEntryContainer");



      
      // ======================================================
      // Build All Categories (including user-defined ones)
      // ======================================================
      function buildAllCategories() {
        return {
          ...CATEGORIES,
          ...ADDITIONAL_CATEGORIES,
          ...loadUserCategories()
        };
      }

      // ======================================================
      // Build All Groups (including user-defined ones, excluding hidden)
      // ======================================================
      function buildAllGroups() {
        // 1. Collect all built-in "key" group names:
        const builtInKeys = new Set();
        Object.values({ ...CATEGORIES, ...ADDITIONAL_CATEGORIES }).forEach(filters => {
          filters.forEach(f => {
            if (f.key) builtInKeys.add(f.key);
          });
        });
        // 2. Get any user-defined group names:
        const userGroups = loadUserGroups();
        // 3. Get hidden groups to filter out:
        const hiddenGroups = loadHiddenGroups();
        // 4. Merge built-in keys and userGroups (ensure unique), excluding hidden:
        return [
          ...Array.from(builtInKeys),
          ...userGroups.filter(g => !builtInKeys.has(g))
        ].filter(group => !hiddenGroups.includes(group));
      }
      
      // ======================================================
      // Initialize Category Checkboxes
      // ======================================================
      function initializeCategoryCheckboxes() {
        // Clear existing categories
        allCategoriesContainer.innerHTML = "";
        
        // Build all categories (including user-defined ones)
        const ALL_CATEGORIES = buildAllCategories();
        
        // Group categories by their key
        const categoriesByKey = groupCategoriesByKey(ALL_CATEGORIES);
        
        // Populate categories by key
        populateCategoriesByKey(categoriesByKey, allCategoriesContainer);
        
        // Set up event listeners for checkbox selection
        setupCheckboxEventListeners();
      }
      
      // Group categories by their key (amenity, shop, office, etc.)
      function groupCategoriesByKey(categoriesObj) {
        const groupedCategories = {};
        const hiddenGroups = loadHiddenGroups();

        Object.entries(categoriesObj).forEach(([filename, filters]) => {
          // Use the first filter's key as the group key
          const groupKey = filters[0].key;

          // Skip hidden groups
          if (hiddenGroups.includes(groupKey)) {
            return;
          }

          if (!groupedCategories[groupKey]) {
            groupedCategories[groupKey] = [];
          }

          groupedCategories[groupKey].push({
            filename,
            filters
          });
        });

        // Ensure all user-defined groups appear even if they have no categories (but not hidden ones)
        const userGroups = loadUserGroups();
        userGroups.forEach(groupKey => {
          if (!groupedCategories[groupKey] && !hiddenGroups.includes(groupKey)) {
            groupedCategories[groupKey] = [];
          }
        });

        // Sort the keys alphabetically
        return Object.fromEntries(
          Object.entries(groupedCategories).sort((a, b) => a[0].localeCompare(b[0]))
        );
      }
      
      // Populate categories grouped by key
      function populateCategoriesByKey(groupedCategories, container) {
        // Create a flex container for all key groups
        const keyGroupsContainer = document.createElement('div');
        keyGroupsContainer.className = 'key-groups-container';
        container.appendChild(keyGroupsContainer);
        
        Object.entries(groupedCategories).forEach(([key, categories]) => {
          // Create a group for this key
          const keyGroup = document.createElement('div');
          keyGroup.className = 'key-group';
          
          // Create header for this key
          const keyHeader = document.createElement('div');
          keyHeader.className = 'key-header';
          keyHeader.innerHTML = `
            <label>
              <input type="checkbox" class="select-key" data-key="${key}">
              ${key.charAt(0).toUpperCase() + key.slice(1)} (${categories.length})
            </label>
            <span class="group-delete-btn" data-key="${key}" title="Delete Group">&times;</span>
            <span class="add-new" data-key="${key}" title="Add New Category">+</span>
          `;
          keyGroup.appendChild(keyHeader);
          
          // Create container for items in this key
          const keyItems = document.createElement('div');
          keyItems.className = 'key-items';
          
          // Determine number of columns based on item count
          if (categories.length <= 9) {
            keyItems.classList.add('columns-1');
          } else if (categories.length <= 18) {
            keyItems.classList.add('columns-2');
          } else {
            keyItems.classList.add('columns-3');
          }
          
          // Add scrollbar if more than 27 items
          if (categories.length > 27) {
            keyItems.classList.add('scrollable');
          }
          
          // Sort categories alphabetically within this key
          categories.sort((a, b) => a.filename.localeCompare(b.filename));
          
          // Add each category in this key
          categories.forEach(({ filename }) => {
            const displayName = filename.replace('.csv', '').replace(/_/g, ' ');
            const item = document.createElement('div');
            item.className = 'category-item';
            item.innerHTML = `
              <label>
                <input type="checkbox" class="category-checkbox"
                       data-key="${key}"
                       data-filename="${filename}">
                <span>${displayName}</span>
              </label>
              <span class="remove-btn" title="Remove">&times;</span>
            `;
            keyItems.appendChild(item);
          });
          
          keyGroup.appendChild(keyItems);
          keyGroupsContainer.appendChild(keyGroup);
        });
      }
      

      
      // ======================================================
      // Setup Checkbox Event Listeners
      // ======================================================
      // Flag to track if main event listeners are set up
      let mainEventListenersSetup = false;

      function setupCheckboxEventListeners() {
        // Set up main event listener only once
        if (!mainEventListenersSetup) {
          selectAllCategoriesCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            document.querySelectorAll('.category-checkbox').forEach(checkbox => {
              checkbox.checked = isChecked;
            });
            document.querySelectorAll('.select-key').forEach(checkbox => {
              checkbox.checked = isChecked;
            });
          });
          mainEventListenersSetup = true;
        }

        // Use event delegation for key group checkboxes and individual checkboxes
        // Remove any existing delegated listeners first
        allCategoriesContainer.removeEventListener('change', handleCategoryChange);
        allCategoriesContainer.addEventListener('change', handleCategoryChange);
      }

      function handleCategoryChange(e) {
        if (e.target.classList.contains('select-key')) {
          const key = e.target.dataset.key;
          const isChecked = e.target.checked;

          document.querySelectorAll(`.category-checkbox[data-key="${key}"]`).forEach(cb => {
            cb.checked = isChecked;
          });

          updateSelectAllCheckbox();
        } else if (e.target.classList.contains('category-checkbox')) {
          updateKeyGroupCheckbox(e.target.dataset.key);
          updateSelectAllCheckbox();
        }
      }
      
      // Update key group checkbox based on individual selections
      function updateKeyGroupCheckbox(key) {
        const keyCheckbox = document.querySelector(`.select-key[data-key="${key}"]`);
        const keyItems = document.querySelectorAll(`.category-checkbox[data-key="${key}"]`);
        
        const allChecked = Array.from(keyItems).every(cb => cb.checked);
        const someChecked = Array.from(keyItems).some(cb => cb.checked);
        
        keyCheckbox.checked = allChecked;
        keyCheckbox.indeterminate = someChecked && !allChecked;
      }
      
      // Update the "Select All" checkbox based on all selections
      function updateSelectAllCheckbox() {
        const allCheckboxes = document.querySelectorAll('.category-checkbox');
        const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
        const someChecked = Array.from(allCheckboxes).some(cb => cb.checked);
        
        selectAllCategoriesCheckbox.checked = allChecked;
        selectAllCategoriesCheckbox.indeterminate = someChecked && !allChecked;
      }

      // ======================================================
      // Logging Utility (use global log function defined earlier)
      // ======================================================
      const log = window.log;

      // ======================================================
      // Delay Helper
      // ======================================================
      function delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
      }

      // ======================================================
      // Build Overpass QL Query for one category
      // ======================================================
      function buildOverpassQuery(filters) {
        // Build filter clauses for the Overpass query
        const filterClauses = filters.map(filter => {
          if (filter.key && filter.value) {
            return `["${filter.key}"="${filter.value}"]`;
          } else if (filter.key) {
            return `["${filter.key}"]`;
          }
          return "";
        }).join("");

        // Get the current coordinates and radius (to ensure we use the latest values)
        const currentLat = window.LAT;
        const currentLng = window.LNG;
        const currentRadius = window.RADIUS;

        // Around: RADIUS meters around LAT,LNG
        return `
[out:json][timeout:25];
(
  node${filterClauses}(around:${currentRadius},${currentLat},${currentLng});
  way${filterClauses}(around:${currentRadius},${currentLat},${currentLng});
  relation${filterClauses}(around:${currentRadius},${currentLat},${currentLng});
);
out center tags;`;
      }

      // ======================================================
      // Fetch from Overpass API
      // ======================================================
      async function fetchOverpass(query) {
        const url = "https://overpass-api.de/api/interpreter";
        const resp = await fetch(url, {
          method: "POST",
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
          body: new URLSearchParams({ data: query })
        });
        if (!resp.ok) {
          throw new Error(`Overpass error: ${resp.status} ${resp.statusText}`);
        }
        return resp.json();
      }

      // ======================================================
      // Parse OSM tags into address fields
      // ======================================================
      function parseOsmTags(tags = {}) {
        // OSM address tags:
        //   addr:housenumber, addr:street, addr:city, addr:state, addr:postcode, contact:phone, contact:fax, contact:email, website
        const name = tags.name || "";
        const streetNumber = tags["addr:housenumber"] || "";
        const streetName   = tags["addr:street"] || "";
        const city         = tags["addr:city"] || "";
        const state        = tags["addr:state"] || "";
        // OSM typically doesn’t include ZIP+4; store full postcode if available
        const postcode     = tags["addr:postcode"] || "";
        const phone        = tags["contact:phone"] || tags.phone || "";
        const fax          = tags["contact:fax"] || "";
        const email        = tags["contact:email"] || "";
        const website      = tags["contact:website"] || tags.website || "";
        const contacts     = ""; // Not stored in standard OSM tags
        return {
          name,
          streetNumber,
          streetName,
          city,
          state,
          postcode,
          phone,
          fax,
          email,
          website,
          contacts
        };
      }

      // ======================================================
      // CSV escaping
      // ======================================================
      function escapeCSV(value) {
        if (value == null) return "";
        const str = value.toString();
        if (str.includes(",") || str.includes('"') || str.includes("\n")) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      }

      function generateCSVString(rows) {
        return rows.map(row => row.map(escapeCSV).join(",")).join("\r\n");
      }

      // ======================================================
      // Process a single category: fetch OSM data, build CSV rows, provide download link
      // ======================================================
      async function processCategory(filename, filters) {
        log(`→ Starting category: ${filename}`);
        const rows = [CSV_HEADER];

        const query = buildOverpassQuery(filters);
        log(`   Querying Overpass for filters: ${JSON.stringify(filters)}`);

        let data;
        try {
          data = await fetchOverpass(query);
        } catch (err) {
          log(`   [!] Overpass fetch error for ${filename}: ${err.message}`);
          return;
        }
        const elements = data.elements || [];
        log(`   Retrieved ${elements.length} elements for ${filename}`);

        for (const el of elements) {
          const tags = el.tags || {};
          const {
            name,
            streetNumber,
            streetName,
            city,
            state,
            postcode,
            phone,
            fax,
            email,
            website,
            contacts
          } = parseOsmTags(tags);
          
          // Skip entries with no name or no address information
          if (!name || (!streetNumber && !streetName && !city)) {
            continue;
          }
          
          rows.push([
            name,
            streetNumber,
            streetName,
            city,
            state,
            postcode,
            phone,
            fax,
            email,
            website,
            contacts
          ]);
        }

        // Skip creating CSV if there are no data rows (only header)
        if (rows.length <= 1) {
          log(`   [i] No valid data for ${filename} - skipping file creation`);
          return;
        }

        const csvContent = generateCSVString(rows);
        const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
        const blobUrl = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = blobUrl;
        link.download = filename;
        link.className = "download-link";

        // Create the enhanced download link structure
        const rowCount = rows.length - 1; // Subtract header row
        link.innerHTML = `
          <div class="download-link-content">
            <div class="download-link-icon">📊</div>
            <div class="download-link-info">
              <div class="download-link-title">${filename}</div>
              <div class="download-link-meta">${rowCount} results • ${(blob.size / 1024).toFixed(1)} KB</div>
            </div>
          </div>
          <div class="download-link-action">Download</div>
        `;

        downloadsDiv.appendChild(link);
        updateResultsContainer();

        log(`✔ Finished category: ${filename} (${rows.length - 1} rows)`);
      }

      // ======================================================
      // Main Runner: iterate selected categories sequentially
      // ======================================================
      async function runExport() {
        // Clear log and download links
        logDiv.textContent = "";
        clearAllResults();
        
        // Get selected categories
        const selectedCheckboxes = document.querySelectorAll('.category-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
          log("[!] No categories selected. Please select at least one category.");
          return;
        }
        
        // Always update ZIP code and coordinates before processing
        const currentZip = zipCodeInput.value.trim();
        if (currentZip !== window.ZIP_CODE) {
          if (/^\d{5}$/.test(currentZip)) {
            log(`Updating coordinates for ZIP code: ${currentZip}...`);
            const result = await window.geocodeZipCode(currentZip);
            if (!result) {
              log(`[!] Failed to geocode ZIP ${currentZip}. Using previous ZIP: ${window.ZIP_CODE}`);
              zipCodeInput.value = window.ZIP_CODE; // Reset input to valid ZIP
            } else {
              log(`✓ Successfully updated coordinates for ZIP ${window.ZIP_CODE}: ${window.LAT}, ${window.LNG}`);
            }
          } else {
            log(`[!] Invalid ZIP code format: ${currentZip}. Using previous ZIP: ${window.ZIP_CODE}`);
            zipCodeInput.value = window.ZIP_CODE; // Reset input to valid ZIP
          }
        }
        
        runBtn.disabled = true;
        const downloadAllFilesBtn = document.getElementById("downloadAllFilesBtn");
        if (downloadAllFilesBtn) {
          downloadAllFilesBtn.disabled = true;
        }

        if (USE_GOOGLE_MAPS) {
          log("=== Export Started (Google Maps) ===");
          if (!googleApiKey) {
            log("[!] Google Maps selected but no API key found. Please save a key to proceed.");
            log("   → Go to Map Provider Settings to add your Google API key");
            runBtn.disabled = false;
            return;
          }

          // Log Google Maps configuration details
          log(`   → API Key: ${googleApiKey.substring(0, 8)}...${googleApiKey.substring(googleApiKey.length - 4)}`);
          log(`   → Search Center: ${window.LAT}, ${window.LNG} (ZIP: ${window.ZIP_CODE})`);
          log(`   → Search Radius: ${window.RADIUS}m (${Math.round(window.RADIUS / 1609.34)} miles)`);

          // Test the API key before proceeding
          const keyTest = await window.testGoogleApiKey(googleApiKey);
          if (!keyTest.valid) {
            log(`   [!] API key test failed: ${keyTest.error}`);
            log("   → Please check your Google API key in Map Provider Settings");
            runBtn.disabled = false;
            return;
          }

        } else {
          log("=== Export Started (Overpass) ===");
          log(`   → Search Center: ${window.LAT}, ${window.LNG} (ZIP: ${window.ZIP_CODE})`);
          log(`   → Search Radius: ${window.RADIUS}m (${Math.round(window.RADIUS / 1609.34)} miles)`);
        }

        // Get fresh merged categories including user-defined ones
        const ALL_CATEGORIES = buildAllCategories();

        for (const checkbox of selectedCheckboxes) {
          const filename = checkbox.dataset.filename;
          const filters = ALL_CATEGORIES[filename];

          try {
            if (USE_GOOGLE_MAPS) {
              const result = await window.fetchGooglePlaces(filters, window.LAT, window.LNG, window.RADIUS);

              // Handle the response from Google Places API
              if (result.success) {
                if (result.results.length > 0) {
                  log(`   ✓ Successfully processed ${filename}: ${result.results.length} results`);
                } else {
                  log(`   ⚠ ${filename}: ${result.message || 'No results found'}`);
                }
              } else {
                log(`   [!] ${filename} failed: ${result.error}`);

                // Optionally offer to fall back to OpenStreetMap for this category
                if (result.error.includes("quota") || result.error.includes("limit")) {
                  log(`   → Consider switching to OpenStreetMap or increasing your Google API quota`);
                } else if (result.error.includes("API key")) {
                  log(`   → Please check your Google API key configuration`);
                }
              }
            } else {
              await processCategory(filename, filters);
            }
            // slight delay to avoid overloading APIs
            await delay(1000);
          } catch (err) {
            log(`   [!] Unexpected error processing ${filename}: ${err.message}`);
            console.error(`Error processing ${filename}:`, err);
          }
        }

        // Count successful downloads
        const downloadLinks = document.querySelectorAll("#downloads .download-link");
        const totalCategories = selectedCheckboxes.length;

        if (USE_GOOGLE_MAPS) {
          log(`=== Google Maps Export Finished ===`);
          log(`   → Processed ${totalCategories} categories`);
          log(`   → Generated ${downloadLinks.length} CSV files`);
          if (downloadLinks.length < totalCategories) {
            log(`   → ${totalCategories - downloadLinks.length} categories had no results or errors`);
          }
        } else {
          log("=== Export Finished. Download links are above. ===");
        }

        runBtn.disabled = false;
        enableDownloadAllButton();
      }

      // ======================================================
      // Setup Dynamic Add/Remove Functionality
      // ======================================================
      function setupDynamicAddRemove() {
        // Delegate click on the "+" to insert a new-entry row
        allCategoriesContainer.addEventListener('click', function(e) {
          if (e.target.classList.contains('add-new')) {
            // 1. Determine which key group we're in:
            const key = e.target.dataset.key;  // e.g. "shop", "amenity", etc.
            // 2. Locate its parent .key-group container and then the .key-items container:
            const keyGroup = e.target.closest('.key-group');
            const keyItems = keyGroup.querySelector('.key-items');

            // 3. If there's already a .new-entry row, do nothing (prevent duplicates)
            if (keyItems.querySelector('.category-item.new-entry')) return;

            // 4. Create a new .category-item.new-entry DIV:
            const newItem = document.createElement('div');
            newItem.className = 'category-item new-entry';
            newItem.innerHTML = `
              <input type="text" class="new-category-input" placeholder="Enter new value">
              <span class="cancel-new" title="Cancel">&times;</span>
              <a href="#" class="ask-chatgpt-link" target="_blank" rel="noopener noreferrer">Ask ChatGPT for OSM tags</a>
            `;
            // 5. Prepend this newItem to keyItems so it appears at the top:
            keyItems.prepend(newItem);

            // 6. Grab references to the input field and the new "Ask ChatGPT" link:
            const inputField = newItem.querySelector('.new-category-input');
            const askLink = newItem.querySelector('.ask-chatgpt-link');

            // 7. Focus the input right away:
            inputField.focus();

            // 8. Define a helper function updateChatGPTLink():
            function updateChatGPTLink() {
              const businessValue = inputField.value.trim();
              let businessCategory = key;  // Always include the group's key
              // Construct the prompt text to ask ChatGPT:
              const promptText =
                `Given a business description of "${businessCategory}` +
                `${businessValue ? ' : ' + businessValue : ''}` +
                `", what is the correct Overpass API (OpenStreetMap) key and value to use when searching for that business? ` +
                `Please reply with a list of valid key:value pairs only, without including any code examples.`;
              // URL-encode the prompt:
              const encodedPrompt = encodeURIComponent(promptText);
              // Set the askLink's href accordingly:
              askLink.href = `https://chat.openai.com/?prompt=${encodedPrompt}`;
            }

            // 9. Bind inputField.addEventListener('input', updateChatGPTLink):
            inputField.addEventListener('input', updateChatGPTLink);

            // Initialize the ChatGPT link with the key only
            updateChatGPTLink();

            // 10. Handle keydown events on inputField:
            inputField.addEventListener('keydown', function(evt) {
              if (evt.key === 'Enter') {
                // a) Grab and trim the new value:
                const newValue = inputField.value.trim();
                if (!newValue) return;  // don't proceed if empty

                // b) Build a filename from newValue (underscores + .csv):
                const filename = newValue.replace(/\s+/g, '_') + '.csv';

                // c) Load userCategories from localStorage:
                const userCategories = loadUserCategories();

                // d) Prevent duplicates: if filename already exists in userCategories or built-in categories, alert:
                if (userCategories[filename] || buildAllCategories()[filename]) {
                  alert('Category already exists.');
                  return;
                }

                // e) Otherwise, save to localStorage:
                userCategories[filename] = [{ key, value: newValue }];
                saveUserCategories(userCategories);

                // f) Reinitialize the category list UI to reflect new addition:
                initializeCategoryCheckboxes();
              } else if (evt.key === 'Escape') {
                // Remove the inline new-entry row without saving
                newItem.remove();
              }
            });

            // 11. Bind click on the cancel icon (.cancel-new) to remove newItem:
            newItem.querySelector('.cancel-new').addEventListener('click', function() {
              newItem.remove();
            });
          }
        });

        // Delegate click on "×" (remove-btn) to delete that category
        allCategoriesContainer.addEventListener('click', function(e) {
          if (e.target.classList.contains('remove-btn')) {
            const itemDiv = e.target.closest('.category-item');
            const filename = itemDiv.querySelector('.category-checkbox')?.dataset.filename;
            if (filename && loadUserCategories()[filename]) {
              const userCategories = loadUserCategories();
              delete userCategories[filename];
              saveUserCategories(userCategories);
            }
            itemDiv.remove();
            updateKeyGroupCheckbox(itemDiv.querySelector('.category-checkbox')?.dataset.key);
            updateSelectAllCheckbox();
          }
        });

        // Delegate click on group delete button to delete entire group
        allCategoriesContainer.addEventListener('click', function(e) {
          if (e.target.classList.contains('group-delete-btn')) {
            const groupKey = e.target.dataset.key;
            const groupName = groupKey.charAt(0).toUpperCase() + groupKey.slice(1);

            // Show confirmation dialog
            const confirmed = confirm(
              `Are you sure you want to delete the entire "${groupName}" group?\n\n` +
              `This will remove:\n` +
              `• The group itself\n` +
              `• All custom categories in this group\n\n` +
              `Built-in categories will be preserved but the group will be hidden.\n\n` +
              `This action cannot be undone.`
            );

            if (confirmed) {
              deleteGroup(groupKey);
            }
          }
        });
      }

      // ======================================================
      // Setup Dynamic Add/Remove Groups Functionality
      // ======================================================
      function setupDynamicAddRemoveGroups() {
        // 1. Grab the "+" in the categories-header:
        const addGroupBtn = document.querySelector(".categories-header .add-group");
        // 2. Listen for clicks:
        addGroupBtn.addEventListener('click', function() {
          // a) Prevent multiple new-entry rows:
          if (headerNewEntryContainer.querySelector('.header-new-entry')) return;
          // b) Create the inline row:
          const newEntryDiv = document.createElement('div');
          newEntryDiv.className = 'header-new-entry';
          newEntryDiv.innerHTML = `
            <input type="text" class="new-group-input" placeholder="Enter new group name">
            <span class="cancel-new" title="Cancel">&times;</span>
            <a href="#" class="ask-chatgpt-link" target="_blank" rel="noopener noreferrer">Ask ChatGPT for OSM tags</a>
          `;
          headerNewEntryContainer.appendChild(newEntryDiv);
          // c) Grab references:
          const inputField = newEntryDiv.querySelector('.new-group-input');
          const askLink = newEntryDiv.querySelector('.ask-chatgpt-link');
          inputField.focus();
          // d) Build a helper to update the ChatGPT link dynamically:
          function updateGroupChatGPTLink() {
            const groupValue = inputField.value.trim();
            const promptText =
              `Given a group description of "${groupValue}", ` +
              `what is the correct Overpass API (OpenStreetMap) key and value ` +
              `to use when searching for that group? ` +
              `Please reply with a list of valid key:value pairs only, ` +
              `without including any code examples.`;
            const encodedPrompt = encodeURIComponent(promptText);
            askLink.href = `https://chat.openai.com/?prompt=${encodedPrompt}`;
          }
          inputField.addEventListener('input', updateGroupChatGPTLink);
          updateGroupChatGPTLink();
          // e) Handle Enter/Escape keys:
          inputField.addEventListener('keydown', function(evt) {
            if (evt.key === 'Enter') {
              const newGroupName = inputField.value.trim();
              if (!newGroupName) return;
              const userGroups = loadUserGroups();
              // Prevent duplicates against built-in groups or existing userGroups
              if (userGroups.includes(newGroupName) || buildAllGroups().includes(newGroupName)) {
                alert('Group already exists.');
                return;
              }
              userGroups.push(newGroupName);
              saveUserGroups(userGroups);
              // Re-render categories & groups:
              headerNewEntryContainer.innerHTML = "";
              initializeCategoryCheckboxes();
            } else if (evt.key === 'Escape') {
              newEntryDiv.remove();
            }
          });
          // f) Bind the cancel icon:
          newEntryDiv.querySelector('.cancel-new').addEventListener('click', function() {
            newEntryDiv.remove();
          });
        });
      }

      // Initialize the UI
      initializeCategoryCheckboxes();
      setupDynamicAddRemove();
      setupDynamicAddRemoveGroups();

      // ======================================================
      // Reset Categories Functionality
      // ======================================================
      const resetCategoriesLink = document.getElementById("resetCategoriesLink");
      resetCategoriesLink.addEventListener("click", function(e) {
        e.preventDefault();
        // 1. Clear stored user categories, groups, and hidden groups:
        localStorage.removeItem("userCategories");
        localStorage.removeItem("userGroups");
        localStorage.removeItem("hiddenGroups");

        // 2. Remove any header-level new-entry if present:
        const headerContainer = document.getElementById("headerNewEntryContainer");
        headerContainer.innerHTML = "";

        // 3. Remove any category-level new-entry rows:
        document.querySelectorAll(".category-item.new-entry").forEach(item => item.remove());

        // 4. Rebuild category checkboxes from scratch:
        initializeCategoryCheckboxes();
      });

      // ======================================================
      // Restore Hidden Groups Functionality
      // ======================================================
      const restoreGroupsLink = document.getElementById("restoreGroupsLink");
      restoreGroupsLink.addEventListener("click", function(e) {
        e.preventDefault();

        const hiddenGroups = loadHiddenGroups();

        if (hiddenGroups.length === 0) {
          alert("No hidden groups to restore.");
          return;
        }

        // Show confirmation dialog with list of hidden groups
        const groupsList = hiddenGroups.map(group =>
          `• ${group.charAt(0).toUpperCase() + group.slice(1)}`
        ).join('\n');

        const confirmed = confirm(
          `Restore the following hidden groups?\n\n${groupsList}\n\n` +
          `This will make them visible again in the categories list.`
        );

        if (confirmed) {
          // Clear hidden groups
          saveHiddenGroups([]);

          // Refresh the UI
          initializeCategoryCheckboxes();

          alert(`Successfully restored ${hiddenGroups.length} group(s).`);
        }
      });

      // ======================================================
      // Event Listeners
      // ======================================================
      runBtn.addEventListener("click", runExport);

      // Clear results button
      const clearResultsBtn = document.getElementById("clearResultsBtn");
      if (clearResultsBtn) {
        clearResultsBtn.addEventListener("click", clearAllResults);
      }

      // ─── Export / Import Configuration ─────────────────────────────────────────────

      // 1. Export current userCategories + userGroups as a .json
      const exportConfigBtn = document.getElementById("exportConfigBtn");
      exportConfigBtn.addEventListener("click", function () {
        try {
          const userCategories = loadUserCategories(); // { filename: [ { key, value }, … ], … }
          const userGroups     = loadUserGroups();     // [ "customGroup1", "customGroup2", … ]
          const payload = {
            userCategories,
            userGroups
          };

          // Stringify with indentation for readability
          const jsonString = JSON.stringify(payload, null, 2);
          const blob = new Blob([jsonString], { type: "application/json" });
          const url  = URL.createObjectURL(blob);

          // Create a temporary <a> to trigger download
          const link = document.createElement("a");
          link.href = url;
          link.download = "business_config.json";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Revoke the object URL after a short delay
          setTimeout(() => URL.revokeObjectURL(url), 1000);
        } catch (err) {
          alert("Error exporting configuration: " + err.message);
          console.error(err);
        }
      });

      // 2. Import a JSON that has the same structure { userCategories, userGroups }
      const importConfigBtn   = document.getElementById("importConfigBtn");
      const importConfigInput = document.getElementById("importConfigInput");

      // When the user clicks "Import Config", forward to the hidden file input
      importConfigBtn.addEventListener("click", function () {
        importConfigInput.click();
      });

      importConfigInput.addEventListener("change", function (e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function (evt) {
          try {
            const imported = JSON.parse(evt.target.result);
            // Check for required properties
            if (
              imported.userCategories !== undefined &&
              imported.userGroups !== undefined
            ) {
              // Overwrite localStorage
              saveUserCategories(imported.userCategories);
              saveUserGroups(imported.userGroups);

              // Re-render the category UI
              initializeCategoryCheckboxes();

              alert("Configuration imported successfully.");
            } else {
              throw new Error("Invalid format. Expected { userCategories, userGroups }.");
            }
          } catch (err) {
            alert("Error parsing JSON: " + err.message);
            console.error(err);
          }
        };
        reader.readAsText(file);

        // Reset the input so the same file can be re-selected if needed
        e.target.value = "";
      });
      // ───────────────────────────────────────────────────────────────────────────────

      // Enable download all button after export is complete
      function enableDownloadAllButton() {
        const downloadAllBtn = document.getElementById("downloadAllFilesBtn");
        if (downloadAllBtn) {
          downloadAllBtn.disabled = false;
        }
      }

      // Download all files as a single ZIP
      async function downloadAllFiles() {
        log("=== Creating ZIP file with all CSVs ===");
        const downloadsDiv = document.getElementById("downloads");
        const links = downloadsDiv.querySelectorAll("a.download-link");

        if (links.length === 0) {
          log("[!] No files available to download. Run the export first.");
          return;
        }

        // Create new JSZip instance
        const zip = new JSZip();

        // Add each CSV to the zip
        for (let i = 0; i < links.length; i++) {
          const link = links[i];
          const filename = link.download;
          log(`Adding ${filename} to ZIP (${i+1}/${links.length})`);

          // Fetch the blob from the object URL
          const response = await fetch(link.href);
          const blob = await response.blob();

          // Add file to zip
          zip.file(filename, blob);
        }

        // Generate the zip file
        log("Generating ZIP file...");
        const zipBlob = await zip.generateAsync({type: "blob"});

        // Create download link for the zip
        const zipUrl = URL.createObjectURL(zipBlob);
        const zipLink = document.createElement("a");
        zipLink.href = zipUrl;
        zipLink.download = "business_data.zip";

        // Trigger download
        document.body.appendChild(zipLink);
        zipLink.click();
        document.body.removeChild(zipLink);

        log("=== ZIP file downloaded ===");
      }

      // Add event listener for download all button
      const downloadAllFilesBtn = document.getElementById("downloadAllFilesBtn");
      if (downloadAllFilesBtn) {
        downloadAllFilesBtn.addEventListener("click", downloadAllFiles);
      }

      // Update radius values in the UI
      document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.radius').forEach(el => {
          el.textContent = RADIUS_MILES;
        });
        document.querySelectorAll('.radius-meters').forEach(el => {
          el.textContent = RADIUS;
        });

        // Initialize map settings
        initializeMapSettings();

        // Handle mapProvider toggle
        const osmProvider = document.getElementById("osmProvider");
        const googleProvider = document.getElementById("googleProvider");

        osmProvider.addEventListener("change", function() {
          if (this.checked) {
            USE_GOOGLE_MAPS = false;
            localStorage.setItem("mapProvider", "osm");
            console.log("Map provider set to:", "OpenStreetMap");
            document.getElementById("runBtn").disabled = false;
          }
        });

        googleProvider.addEventListener("change", function() {
          if (this.checked) {
            USE_GOOGLE_MAPS = true;
            localStorage.setItem("mapProvider", "google");
            console.log("Map provider set to:", "Google Maps");

            if (!googleApiKey) {
              const statusEl = document.getElementById("googleKeyStatus");
              statusEl.textContent = "No API Key found";
              statusEl.className = "status-message danger";
              document.getElementById("runBtn").disabled = true;
              alert("Google Maps selected but no API key saved. Please save a key to proceed.");
            } else {
              document.getElementById("runBtn").disabled = false;
            }
          }
        });

        // Save/update API key, obscure input, manage remove button state
        const saveGoogleKeyBtn = document.getElementById("saveGoogleKeyBtn");
        const removeGoogleKeyBtn = document.getElementById("removeGoogleKeyBtn");
        const googleApiKeyInput = document.getElementById("googleApiKeyInput");
        const googleKeyStatus = document.getElementById("googleKeyStatus");
        const toggleGoogleKeyVisibility = document.getElementById("toggleGoogleKeyVisibility");

        let isKeyVisible = false;

        saveGoogleKeyBtn.addEventListener("click", function() {
          try {
            if (googleApiKeyInput.getAttribute("data-obscured") === "true") {
              // User wants to update: switch to edit mode
              googleApiKeyInput.removeAttribute("data-obscured");
              googleApiKeyInput.value = "";
              googleApiKeyInput.type = "text";
              googleApiKeyInput.focus();
              saveGoogleKeyBtn.textContent = "Save Key";
            } else {
              // Save new key
              const newKey = googleApiKeyInput.value.trim();

              // Validate the API key format
              const validation = validateGoogleApiKey(newKey);
              if (!validation.valid) {
                alert(`Invalid API Key: ${validation.error}`);
                return;
              }

              googleApiKey = newKey;
              localStorage.setItem("googleApiKey", newKey);
              window.obscureKeyInInput();
              removeGoogleKeyBtn.disabled = false;
              saveGoogleKeyBtn.textContent = "Update Key";
              googleKeyStatus.textContent = "Key saved";
              googleKeyStatus.className = "status-message success";

              // Enable run button if Google Maps is selected
              if (USE_GOOGLE_MAPS) {
                document.getElementById("runBtn").disabled = false;
              }

              console.log("Google API key saved.");
            }
          } catch (err) {
            alert("Error saving API key: " + err.message);
          }
        });

        removeGoogleKeyBtn.addEventListener("click", function() {
          try {
            localStorage.removeItem("googleApiKey");
            googleApiKey = null;
            googleApiKeyInput.value = "";
            googleApiKeyInput.removeAttribute("data-obscured");
            googleApiKeyInput.type = "text";
            removeGoogleKeyBtn.disabled = true;
            saveGoogleKeyBtn.textContent = "Save Key";
            googleKeyStatus.textContent = "";
            googleKeyStatus.className = "status-message";

            // Disable run button if Google Maps is selected
            if (USE_GOOGLE_MAPS) {
              document.getElementById("runBtn").disabled = true;
              googleKeyStatus.textContent = "No API Key found";
              googleKeyStatus.className = "status-message danger";
            }

            console.log("Google API key removed.");
          } catch (err) {
            alert("Error removing API key: " + err.message);
          }
        });

        // Show/Hide Key option
        toggleGoogleKeyVisibility.addEventListener("click", function() {
          if (googleApiKeyInput.getAttribute("data-obscured") === "true" && googleApiKey) {
            if (!isKeyVisible) {
              // Show key temporarily
              googleApiKeyInput.value = googleApiKey;
              googleApiKeyInput.type = "text";
              toggleGoogleKeyVisibility.textContent = "🙈";
              isKeyVisible = true;

              // Hide again after 3 seconds
              setTimeout(() => {
                if (isKeyVisible) {
                  window.obscureKeyInInput();
                  toggleGoogleKeyVisibility.textContent = "👁️";
                  isKeyVisible = false;
                }
              }, 3000);
            } else {
              // Hide key immediately
              window.obscureKeyInInput();
              toggleGoogleKeyVisibility.textContent = "👁️";
              isKeyVisible = false;
            }
          }
        });
      });
    })();

    // ======================================================
    // "Story of This Page" Popup Logic
    // ======================================================
    (function() {
      const openBtn = document.getElementById("openPageStory");
      const overlay = document.getElementById("pageStoryOverlay");
      const closeBtn = document.getElementById("closePageStory");
      const dialog = document.getElementById("pageStoryDialog");

      function openStory(e) {
        if (e) e.preventDefault();
        overlay.classList.remove("hidden-overlay");
        overlay.classList.add("visible-overlay");
        // trap focus on dialog
        dialog.focus();
      }

      function closeStory() {
        overlay.classList.remove("visible-overlay");
        overlay.classList.add("hidden-overlay");
        // Return focus to the opener button
        openBtn.focus();
      }

      openBtn.addEventListener("click", openStory);
      closeBtn.addEventListener("click", closeStory);

      // Close on pressing Esc
      document.addEventListener("keydown", function(e) {
        if (e.key === "Escape" && overlay.classList.contains("visible-overlay")) {
          closeStory();
        }
      });

      // Close when clicking outside the dialog
      overlay.addEventListener("click", function(e) {
        if (e.target === overlay) {
          closeStory();
        }
      });

      // Trap focus inside the dialog when open
      dialog.addEventListener("keydown", function(e) {
        if (e.key === "Tab") {
          const focusableElements = dialog.querySelectorAll("button, [href], input, select, textarea, [tabindex]:not([tabindex='-1'])");
          const firstElement = focusableElements[0];
          const lastElement = focusableElements[focusableElements.length - 1];

          if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      });
    })();

    // ======================================================
    // "Instructions" Popup Logic
    // ======================================================
    (function() {
      const openBtn = document.getElementById("openInstructions");
      const overlay = document.getElementById("instructionsOverlay");
      const closeBtn = document.getElementById("closeInstructions");
      const dialog = document.getElementById("instructionsDialog");
      const contentDiv = document.getElementById("instructionsContent");

      // Enhanced markdown to HTML converter using marked.js library
      function parseMarkdown(markdown) {
        // Check if marked.js is available
        if (typeof marked === 'undefined') {
          console.error('marked.js library is not loaded');
          return `<p>Error: Markdown parser not available. Raw content:</p><pre>${markdown}</pre>`;
        }

        // Configure marked.js options
        marked.setOptions({
          breaks: true,        // Convert line breaks to <br>
          gfm: true,          // Enable GitHub Flavored Markdown
          headerIds: true,    // Add IDs to headers for anchor links
          mangle: false,      // Don't mangle autolinked email addresses
          sanitize: false,    // Don't sanitize HTML (we trust our content)
          smartLists: true,   // Use smarter list behavior
          smartypants: false, // Don't use smart quotes
          xhtml: false        // Don't use XHTML-style tags
        });

        // Custom renderer to handle links properly
        const renderer = new marked.Renderer();

        // Override link rendering to handle internal anchors and external links
        renderer.link = function(href, title, text) {
          // Ensure href is a string (marked.js sometimes passes objects)
          const hrefStr = typeof href === 'string' ? href : (href?.href || '');

          if (hrefStr.startsWith('#')) {
            // Internal anchor link - use data attribute for custom handling
            const anchor = hrefStr.substring(1);
            return `<a href="${hrefStr}" class="internal-link" data-anchor="${anchor}"${title ? ` title="${title}"` : ''}>${text}</a>`;
          } else {
            // External link - open in new tab
            return `<a href="${hrefStr}" target="_blank" rel="noopener noreferrer"${title ? ` title="${title}"` : ''}>${text}</a>`;
          }
        };

        // Use marked.js to parse the markdown
        try {
          return marked.parse(markdown, { renderer: renderer });
        } catch (error) {
          console.error('Error parsing markdown with marked.js:', error);
          // Fallback to simple text if parsing fails
          return `<p>Error parsing markdown content. Raw content:</p><pre>${markdown}</pre>`;
        }
      }





      function setupInternalLinkHandlers() {
        // Find all internal anchor links within the instructions content
        const internalLinks = contentDiv.querySelectorAll('a.internal-link');

        internalLinks.forEach(link => {
          link.addEventListener('click', function(e) {
            e.preventDefault();

            const anchor = this.getAttribute('data-anchor');
            if (!anchor) return;

            // Find the target element by ID or by heading text
            let targetElement = contentDiv.querySelector(`#${anchor}`);

            if (!targetElement) {
              // If no element with ID found, try to find by heading text
              // Convert anchor to text format (replace hyphens with spaces, etc.)
              const searchText = anchor
                .replace(/-+/g, ' ')
                .replace(/\b\w/g, l => l.toUpperCase())
                .trim();

              // Look for headings that match
              const headings = contentDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
              for (const heading of headings) {
                const headingText = heading.textContent.trim();
                if (headingText.toLowerCase().includes(searchText.toLowerCase()) ||
                    searchText.toLowerCase().includes(headingText.toLowerCase())) {
                  targetElement = heading;
                  break;
                }
              }
            }

            if (targetElement) {
              // Scroll the instructions content container to the target element
              const instructionsContent = document.querySelector('.instructions-content');
              const targetOffset = targetElement.offsetTop - instructionsContent.offsetTop;

              instructionsContent.scrollTo({
                top: targetOffset - 20, // Add some padding
                behavior: 'smooth'
              });

              // Optional: highlight the target element briefly
              targetElement.style.backgroundColor = 'rgba(0, 75, 141, 0.1)';
              setTimeout(() => {
                targetElement.style.backgroundColor = '';
              }, 2000);
            } else {
              console.warn(`Target element not found for anchor: ${anchor}`);
            }
          });
        });
      }

      async function loadMarkdownContent() {
        try {
          contentDiv.innerHTML = '<div class="loading-message">Loading latest documentation from GitHub...</div>';

          // Add cache-busting parameter to ensure we get the latest version
          const timestamp = new Date().getTime();

          // Try multiple possible README filenames and branches
          const possibleUrls = [
            `https://raw.githubusercontent.com/mytech-today-now/business_search/main/README.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/main/readme.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/main/Readme.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/master/README.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/master/readme.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/master/Readme.md?t=${timestamp}`
          ];

          let response;
          let lastError;
          let attemptedUrls = [];

          // Try each URL until one works
          for (const url of possibleUrls) {
            try {
              console.log(`Attempting to fetch: ${url}`);
              response = await fetch(url);
              attemptedUrls.push(`${url} - Status: ${response.status}`);
              if (response.ok) {
                console.log(`Success! Loaded from: ${url}`);
                break; // Success, exit the loop
              }
              lastError = new Error(`HTTP error! status: ${response.status} for ${url}`);
            } catch (error) {
              lastError = error;
              attemptedUrls.push(`${url} - Error: ${error.message}`);
              console.warn(`Failed to fetch from ${url}:`, error.message);
            }
          }

          if (!response || !response.ok) {
            // Try to fall back to local README.md file
            console.log('GitHub fetch failed, trying local README.md...');
            try {
              const localResponse = await fetch(`/README.md?t=${timestamp}`);
              if (localResponse.ok) {
                console.log('Success! Loaded local README.md');
                const markdownText = await localResponse.text();
                const htmlContent = parseMarkdown(markdownText);
                contentDiv.innerHTML = `
                  <div class="local-fallback-notice" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin-bottom: 20px; border-radius: 4px;">
                    <strong>Note:</strong> Displaying local documentation (GitHub version unavailable)
                  </div>
                  ${htmlContent}
                `;
                // Add event listeners for internal anchor links
                setupInternalLinkHandlers();
                return;
              }
            } catch (localError) {
              console.warn('Local fallback also failed:', localError.message);
              attemptedUrls.push(`Local README.md - Error: ${localError.message}`);
            }
            throw lastError || new Error('All README URLs failed');
          }

          const markdownText = await response.text();
          const htmlContent = parseMarkdown(markdownText);

          contentDiv.innerHTML = htmlContent;

          // Add event listeners for internal anchor links
          setupInternalLinkHandlers();

        } catch (error) {
          console.error('Error loading markdown:', error);

          // Ensure attemptedUrls is defined
          const urlList = (typeof attemptedUrls !== 'undefined' && attemptedUrls.length > 0)
            ? attemptedUrls.map(url => `<li><code>${url}</code></li>`).join('')
            : '<li><code>No URLs attempted (initialization error)</code></li>';

          contentDiv.innerHTML = `
            <div class="error-message">
              <h3>Error Loading Documentation</h3>
              <p>Unable to load the documentation from GitHub. This could be due to:</p>
              <ul>
                <li>Network connectivity issues</li>
                <li>GitHub API rate limiting</li>
                <li>Repository access restrictions</li>
                <li>CORS policy restrictions</li>
                <li>Local file access restrictions</li>
              </ul>
              <p>Please try again later or visit the GitHub repository directly:</p>
              <p><a href="https://github.com/mytech-today-now/business_search" target="_blank" rel="noopener noreferrer">View Documentation on GitHub</a></p>
              <p><a href="https://github.com/mytech-today-now/business_search/blob/main/README.md" target="_blank" rel="noopener noreferrer">Direct link to README.md</a></p>
              <p><em>Note: The documentation is automatically loaded from the latest version in the repository.</em></p>
              <div style="margin-top: 20px;">
                <button onclick="loadEmbeddedDocs()" style="background: var(--primary-color); color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                  Load Basic Documentation
                </button>
              </div>
              <details>
                <summary>Technical Details</summary>
                <p><strong>Error:</strong> ${error.message}</p>
                <p><strong>Attempted URLs:</strong></p>
                <ul>
                  ${urlList}
                </ul>
              </details>
            </div>
          `;
        }
      }

      // Fallback function to load basic embedded documentation
      window.loadEmbeddedDocs = function() {
        const basicDocs = `
          <div class="local-fallback-notice" style="background: #e3f2fd; border: 1px solid #2196f3; padding: 10px; margin-bottom: 20px; border-radius: 4px;">
            <strong>Basic Documentation:</strong> Embedded fallback version
          </div>

          <h1>Business Search & Export Tool</h1>

          <p>A powerful web-based application for generating targeted business mailing lists within specified geographic areas, developed by <a href="https://mytech.today" target="_blank" rel="noopener noreferrer">myTech.Today</a>.</p>

          <h2>Quick Start Guide</h2>

          <h3>1. Set Your Location</h3>
          <ul>
            <li>Enter your ZIP code in the input field (default: 60010 - Barrington, IL)</li>
            <li>Press Enter or click away to automatically update coordinates</li>
            <li>Verify the coordinates display shows your intended location</li>
          </ul>

          <h3>2. Configure Search Radius</h3>
          <ul>
            <li>Adjust the radius slider or input field (1-100 miles)</li>
            <li>Default is 30 miles - suitable for most regional business searches</li>
          </ul>

          <h3>3. Select Business Categories</h3>
          <ul>
            <li><strong>Individual Selection:</strong> Check specific categories like "lawyers.csv" or "medical_practices.csv"</li>
            <li><strong>Group Selection:</strong> Use group headers (e.g., "office", "amenity") to select all categories in that group</li>
            <li><strong>Select All:</strong> Use the master checkbox to select/deselect all categories at once</li>
          </ul>

          <h3>4. Add Custom Categories (Optional)</h3>
          <ul>
            <li>Click the "+" button within any group section</li>
            <li>Enter the business description (e.g., "veterinary clinic", "tax preparation")</li>
            <li>Click "Ask ChatGPT for OSM tags" for help finding the correct OpenStreetMap tags</li>
          </ul>

          <h3>5. Run Export</h3>
          <ul>
            <li>Click "Run Export" button</li>
            <li>Monitor the log area for real-time progress updates</li>
            <li>Wait for all selected categories to complete processing</li>
          </ul>

          <h3>6. Download Your Data</h3>
          <ul>
            <li><strong>Individual Files:</strong> Click individual download links for specific business categories</li>
            <li><strong>Complete Dataset:</strong> Click "Download All as ZIP" for a bundled file containing all CSVs</li>
          </ul>

          <h2>How It Works</h2>
          <p>This application uses OpenStreetMap's Overpass API to fetch business data within a specified geographic radius. The data is then parsed and exported as CSV files containing business information such as name, address, phone, email, and website.</p>

          <h2>Key Features</h2>
          <ul>
            <li><strong>No API Keys Required:</strong> Uses free OpenStreetMap data</li>
            <li><strong>Geographic Targeting:</strong> Search within specific mile radius of any US ZIP code</li>
            <li><strong>Multiple Export Formats:</strong> Individual CSV files or bundled ZIP download</li>
            <li><strong>Custom Categories:</strong> Add your own business types beyond the preset list</li>
            <li><strong>Real-time Progress:</strong> Monitor export progress with detailed logging</li>
          </ul>

          <h2>Support</h2>
          <p>For questions or support, contact:</p>
          <ul>
            <li><strong>myTech.Today</strong></li>
            <li>Email: <a href="mailto:<EMAIL>"><EMAIL></a></li>
            <li>Phone: <a href="tel:***********">(*************</a></li>
            <li>Website: <a href="https://mytech.today" target="_blank" rel="noopener noreferrer">https://mytech.today</a></li>
          </ul>
        `;

        contentDiv.innerHTML = basicDocs;
        setupInternalLinkHandlers();
      };

      function openInstructions(e) {
        if (e) e.preventDefault();
        overlay.classList.remove("hidden-overlay");
        overlay.classList.add("visible-overlay");
        // Load markdown content when opening
        loadMarkdownContent();
        // trap focus on dialog
        dialog.focus();
      }

      function closeInstructions() {
        overlay.classList.remove("visible-overlay");
        overlay.classList.add("hidden-overlay");
        // Return focus to the opener button
        openBtn.focus();
      }

      openBtn.addEventListener("click", openInstructions);
      closeBtn.addEventListener("click", closeInstructions);

      // Close on pressing Esc
      document.addEventListener("keydown", function(e) {
        if (e.key === "Escape" && overlay.classList.contains("visible-overlay")) {
          closeInstructions();
        }
      });

      // Close when clicking outside the dialog
      overlay.addEventListener("click", function(e) {
        if (e.target === overlay) {
          closeInstructions();
        }
      });

      // Trap focus inside the dialog when open
      dialog.addEventListener("keydown", function(e) {
        if (e.key === "Tab") {
          const focusableElements = dialog.querySelectorAll("button, [href], input, select, textarea, [tabindex]:not([tabindex='-1'])");
          const firstElement = focusableElements[0];
          const lastElement = focusableElements[focusableElements.length - 1];

          if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      });

      // Verify marked.js integration on page load
      document.addEventListener('DOMContentLoaded', function() {
        if (typeof marked !== 'undefined') {
          console.log('✅ marked.js library loaded successfully');
        } else {
          console.error('❌ marked.js library not loaded');
        }
      });

      // Add footer link functionality
      document.addEventListener('DOMContentLoaded', function() {
        // Footer Instructions link
        const footerInstructionsBtn = document.getElementById("openInstructionsFooter");
        if (footerInstructionsBtn) {
          footerInstructionsBtn.addEventListener("click", openInstructions);
        }

        // Footer Page Story link
        const footerPageStoryBtn = document.getElementById("openPageStoryFooter");
        if (footerPageStoryBtn) {
          footerPageStoryBtn.addEventListener("click", function(e) {
            e.preventDefault();
            // Trigger the existing page story functionality
            const pageStoryBtn = document.getElementById("openPageStory");
            if (pageStoryBtn) {
              pageStoryBtn.click();
            }
          });
        }
      });
    })();
  </script>
</body>
</html>